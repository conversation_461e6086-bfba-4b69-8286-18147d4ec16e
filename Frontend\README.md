# IIM-LMS Frontend

The frontend application for the IIM Learning Management System, built with React, Redux Toolkit, and Tailwind CSS.

## Features

- **Modern UI**: Clean and professional user interface built with Tailwind CSS
- **Responsive Design**: Fully responsive layout compatible with desktop, tablet, and mobile devices
- **State Management**: Centralized state management with Redux Toolkit
- **API Integration**: Secure API communication with axios and interceptors
- **Authentication**: JWT-based authentication with refresh token support and persistent sessions
- **Role-Based Access**: Different interfaces for Educators, University Admins, Super Admins, and My Team
- **Protected Routes**: Secure route protection based on user roles and permissions
- **Form Validation**: Client-side validation for all forms with real-time feedback
- **Notifications**: Toast notifications for user feedback and status updates
- **Newsletter Integration**: Smart newsletter subscription with status persistence
- **Forum System**: Interactive course forums with real-time discussions
- **Certificate Viewing**: Certificate display and verification system
- **Progress Tracking**: Visual progress indicators and completion tracking

## Pages and Components

### Authentication
- Login
- Forgot Password
- Reset Password

### Educator Interface
- Dashboard with progress overview
- My Learning (enrolled courses)
- Content Creation and Management
- Course Detail with forum access
- Course Learning with module progression
- Quiz Taking with certificate generation
- Profile Settings and Preferences

### University Admin Interface
- Educator Management (My Team)
- Profile Settings
- Institution Overview

### Super Admin Interface
- University Management
- Content Approval Workflow
- Course Management
- Newsletter Management
- My Team Administration
- System Analytics

### Shared Components
- Newsletter Subscription Modal
- Course Forums
- Certificate Viewer
- Progress Tracking
- Notification System

## Getting Started

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn
- Backend API running (see Backend README)

### Installation

1. Clone the repository
```bash
git clone https://github.com/your-username/IIM-LMS.git
cd IIM-LMS/Frontend
```

2. Install dependencies
```bash
npm install
# or
yarn install
```

3. Create a `.env` file in the Frontend directory based on `.env.example`:
```bash
cp .env.example .env
```

Update the `.env` file with your configuration:
```
VITE_API_URL=http://localhost:5000/api
VITE_APP_NAME=IIM Education Platform
VITE_ENABLE_NEWSLETTER=true
VITE_ENABLE_FORUMS=true
VITE_ENABLE_CERTIFICATES=true
VITE_ENABLE_QUIZZES=true
```

4. Start the development server
```bash
npm run dev
# or
yarn dev
```

The application will start on http://localhost:5173 by default.

## Building for Production

To create a production build:

```bash
npm run build
# or
yarn build
```

The build artifacts will be stored in the `dist/` directory.

## Project Structure

```
src/
├── assets/         # Static assets and styles
│   └── styles/     # Component-specific CSS files
├── components/     # Reusable components
│   ├── Newsletter/ # Newsletter subscription components
│   ├── Forum/      # Forum and discussion components
│   ├── Certificate/# Certificate display components
│   └── ...         # Other shared components
├── context/        # React context providers
├── hooks/          # Custom React hooks
├── pages/          # Page components
├── redux/          # Redux store and slices
│   ├── auth/       # Authentication state
│   ├── educator/   # Educator state
│   ├── university/ # University state
│   ├── admin/      # Admin state
│   └── newsletter/ # Newsletter state
├── utils/          # Utility functions and helpers
├── App.jsx         # Main application component
├── index.css       # Global styles and Tailwind imports
└── main.jsx        # Application entry point
```

## License

This project is licensed under the MIT License.
