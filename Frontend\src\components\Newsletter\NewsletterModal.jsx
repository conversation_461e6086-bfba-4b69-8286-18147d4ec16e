import React, { useState, useEffect } from 'react';
import { FaEnvelope, FaCheck, FaTimes, FaUser, FaInfoCircle } from 'react-icons/fa';
import { toast } from 'react-toastify';

const NewsletterModal = ({ isOpen, onClose }) => {
  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [subscriptionStatus, setSubscriptionStatus] = useState(null);
  const [isCheckingStatus, setIsCheckingStatus] = useState(false);
  const [showSubscribeForm, setShowSubscribeForm] = useState(true);

  // Check subscription status when modal opens
  useEffect(() => {
    if (isOpen) {
      // Check if there's a stored subscription status
      const storedEmail = localStorage.getItem('newsletter_subscribed_email');
      if (storedEmail) {
        checkSubscriptionStatus(storedEmail);
      }
    }
  }, [isOpen]);

  // Check subscription status for an email
  const checkSubscriptionStatus = async (emailToCheck) => {
    if (!emailToCheck || !emailToCheck.trim()) return;

    const emailRegex = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/;
    if (!emailRegex.test(emailToCheck.trim())) return;

    setIsCheckingStatus(true);
    try {
      const response = await fetch(
        `${import.meta.env.VITE_API_URL}/subscriber/check-status?email=${encodeURIComponent(emailToCheck.trim())}`
      );
      const data = await response.json();

      if (response.ok) {
        setSubscriptionStatus(data);
        if (data.isSubscribed) {
          setEmail(emailToCheck.trim());
          setName(data.name || '');
          setShowSubscribeForm(false);
        }
      }
    } catch (error) {
      console.error('Error checking subscription status:', error);
    } finally {
      setIsCheckingStatus(false);
    }
  };

  // Handle email input change with debounced status check
  const handleEmailChange = (e) => {
    const newEmail = e.target.value;
    setEmail(newEmail);

    // Reset status when email changes
    if (subscriptionStatus && subscriptionStatus.email !== newEmail.trim()) {
      setSubscriptionStatus(null);
      setShowSubscribeForm(true);
    }

    // Debounced status check
    if (newEmail.trim()) {
      clearTimeout(window.emailCheckTimeout);
      window.emailCheckTimeout = setTimeout(() => {
        checkSubscriptionStatus(newEmail);
      }, 1000);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!email.trim()) {
      toast.error('Email is required');
      return;
    }

    const emailRegex = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/;
    if (!emailRegex.test(email.trim())) {
      toast.error('Please provide a valid email address');
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/subscriber/subscribe`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email.trim(),
          name: name.trim() || undefined,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setIsSubscribed(true);
        // Store subscription status in localStorage
        localStorage.setItem('newsletter_subscribed_email', email.trim());
        toast.success(data.message || 'Successfully subscribed to newsletter!');
      } else {
        toast.error(data.message || 'Failed to subscribe. Please try again.');
      }
    } catch (error) {
      console.error('Newsletter subscription error:', error);
      toast.error('Network error. Please check your connection and try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReset = () => {
    setIsSubscribed(false);
    setEmail('');
    setName('');
    setSubscriptionStatus(null);
    setShowSubscribeForm(true);
  };

  const handleClose = () => {
    handleReset();
    onClose();
  };

  const handleSubscribeAnother = () => {
    setEmail('');
    setName('');
    setSubscriptionStatus(null);
    setShowSubscribeForm(true);
    setIsSubscribed(false);
  };

  if (!isOpen) return null;

  return (
    <div className="newsletter-modal-overlay" onClick={handleClose}>
      <div className="newsletter-modal-content" onClick={(e) => e.stopPropagation()}>
        {/* Modal Header */}
        <div className="newsletter-modal-header">
          <div className="newsletter-modal-title">
            <FaEnvelope className="newsletter-modal-icon" />
            <h3>Stay Updated with IIM Education</h3>
          </div>
          <button className="newsletter-modal-close" onClick={handleClose}>
            <FaTimes />
          </button>
        </div>

        {/* Modal Body */}
        <div className="newsletter-modal-body">
          {!isSubscribed ? (
            <>
              <p className="newsletter-modal-description">
                Subscribe to our newsletter and get notified about new courses,
                announcements, and educational content!
              </p>

              {/* Subscription Status Display */}
              {subscriptionStatus && subscriptionStatus.isSubscribed && !showSubscribeForm && (
                <div className="newsletter-status-info">
                  <div className="status-icon">
                    <FaInfoCircle />
                  </div>
                  <div className="status-content">
                    <h4>Already Subscribed!</h4>
                    <p>
                      The email <strong>{subscriptionStatus.email}</strong> is already subscribed to our newsletter.
                      {subscriptionStatus.name && ` Welcome back, ${subscriptionStatus.name}!`}
                    </p>
                    <p className="subscription-date">
                      Subscribed on: {new Date(subscriptionStatus.subscribedAt).toLocaleDateString()}
                    </p>
                    <div className="status-actions">
                      <button
                        type="button"
                        onClick={handleSubscribeAnother}
                        className="newsletter-secondary-btn"
                      >
                        Subscribe with Different Email
                      </button>
                      <button
                        type="button"
                        onClick={handleClose}
                        className="newsletter-primary-btn"
                      >
                        Close
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {/* Subscription Form */}
              {showSubscribeForm && (
                <form onSubmit={handleSubmit} className="newsletter-modal-form">
                  <div className="newsletter-form-group">
                    <label htmlFor="newsletter-name">
                      <FaUser className="form-icon" />
                      Name (Optional)
                    </label>
                    <input
                      type="text"
                      id="newsletter-name"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      placeholder="Enter your name"
                      className="newsletter-form-input"
                    />
                  </div>

                  <div className="newsletter-form-group">
                    <label htmlFor="newsletter-email">
                      <FaEnvelope className="form-icon" />
                      Email Address *
                    </label>
                    <input
                      type="email"
                      id="newsletter-email"
                      value={email}
                      onChange={handleEmailChange}
                      placeholder="Enter your email address"
                      className="newsletter-form-input"
                      required
                    />
                    {isCheckingStatus && (
                      <div className="checking-status">
                        <span>Checking subscription status...</span>
                      </div>
                    )}
                  </div>

                  <button
                    type="submit"
                    disabled={isSubmitting || isCheckingStatus}
                    className="newsletter-submit-btn"
                  >
                    {isSubmitting ? 'Subscribing...' : 'Subscribe Now'}
                  </button>
                </form>
              )}

              {showSubscribeForm && (
                <p className="newsletter-privacy">
                  We respect your privacy. Unsubscribe at any time.
                </p>
              )}
            </>
          ) : (
            <div className="newsletter-success">
              <div className="success-icon">
                <FaCheck />
              </div>
              <h4>Thank You!</h4>
              <p>
                You've successfully subscribed to our newsletter. 
                Check your email for a confirmation message.
              </p>
              <div className="newsletter-success-actions">
                <button 
                  className="newsletter-reset-btn"
                  onClick={handleReset}
                >
                  Subscribe Another Email
                </button>
                <button 
                  className="newsletter-close-btn"
                  onClick={handleClose}
                >
                  Close
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default NewsletterModal;
