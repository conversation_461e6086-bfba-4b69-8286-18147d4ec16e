const mongoose = require('mongoose');

const userModuleProgressSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  course: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course',
    required: true
  },
  moduleProgress: [{
    module: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Module'
    },
    isCompleted: {
      type: Boolean,
      default: false
    },
    completedContent: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Content'
    }],
    // Quiz completion tracking
    quizProgress: {
      hasAttempted: { type: Boolean, default: false },
      hasPassed: { type: Boolean, default: false },
      bestScore: { type: Number, default: 0 },
      lastAttemptScore: { type: Number, default: 0 },
      attemptCount: { type: Number, default: 0 },
      lastAttemptAt: { type: Date },
      passedAt: { type: Date }
    },
    // Module completion status
    contentCompleted: { type: Boolean, default: false },
    quizCompleted: { type: <PERSON>olean, default: false },
    meetsCompletionCriteria: { type: Boolean, default: false },
    lastAccessedAt: {
      type: Date,
      default: Date.now
    }
  }],
  lastAccessedModule: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Module'
  }
}, { timestamps: true });

// Compound index to ensure a user can only have one progress record per course
userModuleProgressSchema.index({ user: 1, course: 1 }, { unique: true });

module.exports = mongoose.model('UserModuleProgress', userModuleProgressSchema);
