const mongoose = require('mongoose');

const quizSchema = new mongoose.Schema({
  title: { type: String, required: true },
  description: { type: String },
  course: { type: mongoose.Schema.Types.ObjectId, ref: 'Course', required: true },
  questions: [{
    question: { type: String, required: true },
    options: [{
      text: { type: String, required: true },
      weight: { type: Number, default: 0 }, // Accuracy weight for partial credit (0-100)
      isCorrect: { type: Boolean, default: false }
    }],
    questionType: {
      type: String,
      enum: ['single-answer', 'multi-answer'],
      default: 'single-answer'
    },
    // Legacy fields for backward compatibility
    correctAnswer: { type: String }, // Deprecated but kept for compatibility
    explanation: { type: String },
    points: { type: Number, default: 1 }
  }],
  timeLimit: { type: Number }, // in minutes
  passingScore: { type: Number, default: 60 }, // percentage
  // Module-specific configuration
  moduleRequirement: {
    isRequired: { type: Boolean, default: true }, // Whether quiz completion is required for module completion
    customPassingScore: { type: Number }, // Override default passing score for this specific quiz
    allowRetakes: { type: Boolean, default: true },
    maxAttempts: { type: Number, default: -1 } // -1 for unlimited attempts
  },
  attempts: [{
    user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    score: { type: Number }, // Final percentage score
    detailedScore: {
      totalPoints: { type: Number },
      earnedPoints: { type: Number },
      partialCredits: [{
        questionIndex: { type: Number },
        maxPoints: { type: Number },
        earnedPoints: { type: Number },
        selectedOptions: [{ type: Number }] // Array of selected option indices
      }]
    },
    answers: [{
      questionIndex: { type: Number },
      selectedAnswer: { type: mongoose.Schema.Types.Mixed }, // Allow both string and array for backward compatibility
      selectedAnswers: [{ type: String }], // New field for multi-answer support
      isCorrect: { type: Boolean },
      partialCredit: { type: Number, default: 0 } // Partial credit earned (0-100)
    }],
    passed: { type: Boolean },
    completedAt: { type: Date, default: Date.now }
  }]
}, { timestamps: true });

// Validation middleware to ensure option weights sum to 100% for each question
quizSchema.pre('save', function(next) {
  for (let question of this.questions) {
    if (question.options && question.options.length > 0) {
      const totalWeight = question.options.reduce((sum, option) => sum + (option.weight || 0), 0);
      const hasCorrectOptions = question.options.some(option => option.isCorrect);

      // Only validate weights if there are correct options marked
      if (hasCorrectOptions && totalWeight !== 100 && totalWeight !== 0) {
        return next(new Error(`Question "${question.question}" has incorrect weight distribution. Total weight must be 100%, got ${totalWeight}%`));
      }
    }
  }
  next();
});

module.exports = mongoose.model('Quiz', quizSchema);