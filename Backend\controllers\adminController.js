const Content = require("../models/Content");
const Course = require("../models/Course");
const User = require("../models/User");
const Module = require("../models/Module");
const Quiz = require("../models/Quiz");
const bcrypt = require("bcryptjs");
const mongoose = require("mongoose");
const { formatPhoneNumber } = require("../utils/phoneUtils");

exports.getUniversities = async (req, res) => {
  try {
    // Return all users with role='university' regardless of status
    const universities = await User.find({ role: "university" })
      .populate("educators", "name email phoneNumber status")
      .select(
        "-password -refreshToken -otp -otpExpires -passwordResetToken -passwordResetExpires"
      );
    res.json(universities);
  } catch (error) {
    res
      .status(500)
      .json({ message: "Error retrieving universities", error: error.message });
  }
};

exports.createUniversity = async (req, res, next) => {
  try {
    const name = req.body.name || req.body.schoolName;
    const email = req.body.email;
    const roleId = req.body.roleId;
    const phoneNumber =
      req.body.phoneNumber ||
      (req.body.phone ? req.body.phone.replace(/^\+91\s*/, "") : null);
    const address = req.body.address;
    const zipcode = req.body.zipcode;
    const state = req.body.state;
    const contactPerson = req.body.contactPerson || req.body.ownerName;

    if (!name || !email || !phoneNumber) {
      return res.status(400).json({
        message: "Missing required fields",
        details: {
          name: name ? "provided" : "missing",
          email: email ? "provided" : "missing",
          phoneNumber: phoneNumber ? "provided" : "missing",
        },
      });
    }

    // Check if university with this email already exists
    const existingUniversity = await User.findOne({
      email,
      role: "university",
    });
    if (existingUniversity) {
      return res
        .status(400)
        .json({ message: "University with this email already exists" });
    }

    // Create a new university user without password (using OTP-based login)
    // Using standardized role mapping: UI: School Admin -> DB: university
    const universityUser = new User({
      email,
      role: "university", // DB role value for School Admin
      name,
      phoneNumber,
      roleRef: roleId || undefined, // Assign role if provided
      contactPerson,
      educators: [],
      profile: {
        address,
        zipcode,
        state,
        avatar: req.file ? `uploads/profiles/${req.file.filename}` : null,
      },
    });

    await universityUser.save();

    // Return the university user without sensitive fields
    const universityData = await User.findById(universityUser._id).select(
      "-password -refreshToken -otp -otpExpires -passwordResetToken -passwordResetExpires"
    );

    res.json(universityData);
  } catch (error) {
    next(error);
  }
};

exports.getUniversityById = async (req, res) => {
  try {
    const university = await User.findOne({
      _id: req.params.id,
      role: "university",
    })
      .populate("educators", "name email phoneNumber status")
      .select(
        "-password -refreshToken -otp -otpExpires -passwordResetToken -passwordResetExpires"
      );

    if (!university) {
      return res.status(404).json({ message: "University not found" });
    }

    res.json(university);
  } catch (error) {
    res
      .status(500)
      .json({ message: "Error retrieving university", error: error.message });
  }
};

exports.deleteUniversity = async (req, res) => {
  try {
    const university = await User.findOne({
      _id: req.params.id,
      role: "university",
    });

    if (!university) {
      return res.status(404).json({ message: "University not found" });
    }

    // Soft delete - update status to 0 (inactive)
    university.status = 0;
    await university.save();

    res.json({ message: "University deleted successfully" });
  } catch (error) {
    res
      .status(500)
      .json({ message: "Error deleting university", error: error.message });
  }
};

exports.updateUniversity = async (req, res) => {
  try {
    // Extract fields from form data
    // Handle both direct fields and FormData
    const name = req.body.name || req.body.schoolName;
    const email = req.body.email;
    const phone = req.body.phone;
    const phoneNumber =
      req.body.phoneNumber || (phone ? phone.replace(/^\+91\s*/, "") : null);
    const address = req.body.address;
    const zipcode = req.body.zipcode;
    const state = req.body.state;
    const contactPerson = req.body.contactPerson || req.body.ownerName;
    const status = req.body.status;
    const keepExistingImage = req.body.keepExistingImage;

    const university = await User.findOne({
      _id: req.params.id,
      role: "university",
    });

    if (!university) {
      return res.status(404).json({ message: "University not found" });
    }

    // Update basic fields
    if (name) university.name = name;
    if (email) university.email = email;
    if (phoneNumber) university.phoneNumber = phoneNumber;
    if (phone) university.phoneNumber = phone.replace(/^\+91\s*/, "");
    if (contactPerson) university.contactPerson = contactPerson;

    // Update profile fields
    university.profile = university.profile || {};
    if (address) university.profile.address = address;
    if (zipcode) university.profile.zipcode = zipcode;
    if (state) university.profile.state = state;

    // Update profile image if provided
    if (req.file) {
      university.profile.avatar = `uploads/profiles/${req.file.filename}`;
    } else if (req.body.keepExistingImage === "true") {
      // Keep existing image, no need to update
    }

    // Handle status update if provided
    if (status !== undefined) {
      university.status = Number(status);
    }

    // Handle roleId update if provided
    if (req.body.roleId) {
      university.roleRef = req.body.roleId;
      // Note: We no longer update the core role field, it remains fixed as "university"
    }

    await university.save();

    // Return updated university without sensitive fields
    const updatedUniversity = await User.findById(university._id)
      .populate("educators", "name email phoneNumber status")
      .select(
        "-password -refreshToken -otp -otpExpires -passwordResetToken -passwordResetExpires"
      );

    res.json(updatedUniversity);
  } catch (error) {
    res
      .status(500)
      .json({ message: "Error updating university", error: error.message });
  }
};

exports.getContent = async (req, res) => {
  try {
    const { search, filter } = req.query;
    let query = { activeStatus: 1 }; // Only return active content

    if (search) query.title = { $regex: search, $options: "i" };
    if (filter) query.status = filter;

    const content = await Content.find(query).populate("creator", "name");
    res.json(content);
  } catch (error) {
    res
      .status(500)
      .json({ message: "Error retrieving content", error: error.message });
  }
};

exports.createContent = async (req, res) => {
  try {
    const { title, description, moduleId, type } = req.body;
    const fileUrl = req.file ? req.file.path : null;

    if (!fileUrl) {
      return res.status(400).json({ msg: "File upload failed" });
    }

    // Determine media type based on file extension
    let mediaType = "document";
    let mimeType = req.file.mimetype;
    const fileExt = req.file.originalname.split(".").pop().toLowerCase();

    if (["mp4", "mov", "avi", "mkv"].includes(fileExt)) {
      mediaType = "video";
    } else if (["jpg", "jpeg", "png", "gif", "webp"].includes(fileExt)) {
      mediaType = "image";
    }

    const content = new Content({
      title,
      description,
      fileUrl,
      creator: req.user.id,
      status: "approved",
      type:
        type ||
        (mediaType === "video"
          ? "video"
          : mediaType === "image"
          ? "image"
          : "document"),
      mediaType,
      mimeType,
      size: req.file.size,
      module: moduleId || null,
    });

    await content.save();

    // If moduleId is provided, add content to the module
    if (moduleId) {
      const module = await Module.findById(moduleId);
      if (module) {
        if (!module.content.includes(content._id)) {
          module.content.push(content._id);
          await module.save();
        }
      }
    }

    res.json(content);
  } catch (error) {
    res.status(500).json({ msg: error.message || "Server Error" });
  }
};

exports.updateContent = async (req, res) => {
  const { title, description } = req.body;
  const content = await Content.findById(req.params.id);
  if (!content) return res.status(404).json({ msg: "Content not found" });
  content.title = title || content.title;
  content.description = description || content.description;
  await content.save();
  res.json(content);
};
exports.approveContent = async (req, res) => {
  const content = await Content.findById(req.params.id);
  content.status = "approved";
  await content.save();
  res.json(content);
};

exports.rejectContent = async (req, res) => {
  const content = await Content.findById(req.params.id);
  content.status = "rejected";
  await content.save();
  res.json(content);
};

exports.deleteContent = async (req, res) => {
  try {
    const content = await Content.findById(req.params.id);

    if (!content) {
      return res.status(404).json({ message: "Content not found" });
    }

    // Soft delete - update activeStatus to 0 (inactive)
    content.activeStatus = 0;
    await content.save();

    res.json({ message: "Content deleted successfully" });
  } catch (error) {
    res
      .status(500)
      .json({ message: "Error deleting content", error: error.message });
  }
};

// Get all courses (both active and inactive, including drafts)
exports.getCourses = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;

    let query = {};

    // If the user is an admin, return all courses except deleted ones
    if (userRole === "admin") {
      query = { status: { $ne: -1 } }; // Exclude courses with status -1 (deleted)
    }
    // If the user is an educator/tutor, apply visibility rules
    else {
      query = {
        status: { $ne: -1 }, // Exclude deleted courses
        $or: [
          // Published courses should be active (status: 1)
          { isDraft: false, status: 1 },
          // Draft courses can be viewed by their creator regardless of status (except deleted)
          { isDraft: true, creator: userId },
        ],
      };
    }

    const courses = await Course.find(query).populate("creator", "name");

    // Add a flag to indicate if a course is a draft
    const coursesWithDraftStatus = courses.map((course) => {
      const courseObj = course.toObject();
      return {
        ...courseObj,
        isDraftLabel: course.isDraft ? "Draft" : "Published",
      };
    });

    res.json(coursesWithDraftStatus);
  } catch (error) {
    console.error("Error getting courses:", error);
    res
      .status(500)
      .json({ message: "Error retrieving courses", error: error.message });
  }
};

// Get a specific course with its content, modules, and quizzes
exports.getCourse = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;

    let query = { _id: req.params.id, status: { $ne: -1 } }; // Exclude deleted courses

    // If the user is not an admin, apply additional visibility rules
    if (userRole !== "admin") {
      query = {
        _id: req.params.id,
        status: { $ne: -1 }, // Exclude deleted courses
        $or: [
          // Published courses should be active (status: 1)
          { isDraft: false, status: 1 },
          // Draft courses can be viewed by their creator regardless of status (except deleted)
          { isDraft: true, creator: userId },
        ],
      };
    }

    const course = await Course.findOne(query)
      .populate({
        path: "creator",
        select:
          "name firstName lastName email role phoneNumber profile.avatar profile.state profile.bio profile.designation profile.department profile.category profile.schoolName profile.socialLinks contactPerson profile.zipcode profile.address", // Include detailed creator information
      })
      .populate("content")
      .populate({
        path: "modules",
        populate: [
          {
            path: "content",
            model: "Content",
          },
          {
            path: "quiz",
            model: "Quiz",
          },
        ],
      })
      .populate({
        path: "quizzes",
        // Include all quiz fields including questions
        select: "title description questions timeLimit passingScore attempts",
      })
      .populate({
        path: "comments.user",
        select: "name profile.avatar", // Include user name and avatar for comments
      })
      .populate({
        path: "enrolledUsers.user",
        select: "name", // 👈 Enrolled user names and avatars
      });

    if (!course) {
      return res
        .status(404)
        .json({
          message: "Course not found or you do not have permission to view it",
        });
    }

    res.json(course);
  } catch (error) {
    console.error("Error retrieving course:", error);
    res
      .status(500)
      .json({ message: "Error retrieving course", error: error.message });
  }
};

exports.createCourse = async (req, res) => {
  try {
    // Destructure fields from request body
    const {
      title,
      description,
      duration,
      language,
      thumbnailUrl,
      hasModules,
      modules,
      content,
      quizzes,
      status,
      isDraft,
    } = req.body;

    // ✅ Use thumbnail from req.files (upload.any())
    let thumbnail = thumbnailUrl;
    const thumbnailFile = req.files?.find((f) => f.fieldname === "thumbnail");
    if (thumbnailFile) {
      thumbnail = thumbnailFile.path;
    }

    // 🧠 Helper function to parse JSON safely
    const tryParse = (data) => {
      try {
        return typeof data === "string" ? JSON.parse(data) : data;
      } catch (err) {
        console.error("Parse error:", err.message);
        return [];
      }
    };

    const parsedModules = tryParse(modules);
    const parsedContent = tryParse(content);
    const parsedQuizzes = tryParse(quizzes);

    // 🔎 Parse contentFileIds
    let contentFileIds = req.body.contentFileIds || [];
    if (!Array.isArray(contentFileIds)) {
      contentFileIds = [contentFileIds];
    }

    const contentItems = [];

    // Process uploaded files and text content
    if (parsedContent && parsedContent.length > 0) {
      // Process text content items first - they don't need file uploads
      const textContentPromises = parsedContent
        .filter((item) => item.type === "text" || item.type === "youtube")
        .map(async (item) => {
          // Different handling for YouTube vs regular text content
          if (item.type === "youtube") {
            const youtubeContentDoc = new Content({
              title: item.title,
              description: item.description,
              textContent: item.textContent || "", // Store YouTube URL in textContent
              fileUrl: item.textContent || "", // Also store in fileUrl for backward compatibility
              creator: req.user.id,
              status: "approved",
              type: "youtube",
              mediaType: "video",
              mimeType: "video/youtube",
              module: null,
            });

            await youtubeContentDoc.save();

            return {
              id: item._id,
              dbId: youtubeContentDoc._id,
            };
          } else {
            // Regular text content
            const textContentDoc = new Content({
              title: item.title,
              description: item.description,
              textContent: item.textContent || "",
              creator: req.user.id,
              status: "approved",
              type: "text",
              mediaType: "text",
              mimeType: "text/html",
              module: null,
            });

            await textContentDoc.save();

            return {
              id: item._id,
              dbId: textContentDoc._id,
            };
          }
        });

      const textContentItems = await Promise.all(textContentPromises);
      contentItems.push(...textContentItems);
    }

    // Process uploaded files
    if (req.files && req.files.length > 0) {
      for (let i = 0; i < req.files.length; i++) {
        const file = req.files[i];
        if (file.fieldname === "thumbnail") continue;

        const fieldnameParts = file.fieldname.match(/\[(\d+)\]/);
        if (!fieldnameParts) continue;

        const index = parseInt(fieldnameParts[1]);
        const contentId = contentFileIds[index];
        if (!contentId) continue;

        const contentItem = parsedContent.find(
          (item) => item._id === contentId
        );
        if (!contentItem) continue;

        const fileExt = file.originalname.split(".").pop().toLowerCase();
        const mimeType = file.mimetype;

        let mediaType = "document";
        if (["mp4", "mov", "avi", "mkv"].includes(fileExt)) mediaType = "video";
        else if (["jpg", "jpeg", "png", "gif", "webp"].includes(fileExt))
          mediaType = "image";

        const contentDoc = new Content({
          title: contentItem.title,
          description: contentItem.description,
          fileUrl: file.path,
          creator: req.user.id,
          status: "approved",
          type: contentItem.type || mediaType,
          mediaType,
          mimeType,
          size: file.size,
          module: null,
        });

        await contentDoc.save();
        contentItems.push({ id: contentId, dbId: contentDoc._id });
      }
    }

    // ✅ Create course
    const course = new Course({
      title,
      description,
      duration,
      language: language || "en",
      thumbnail,
      hasModules: true, // Always use modules
      modules: [],
      content: contentItems.map((item) => item.dbId),
      quizzes: [],
      status: status !== undefined ? Number(status) : 1,
      isDraft: isDraft === "true" || isDraft === true,
      creator: req.user.id,
    });

    await course.save();

    // ✅ Handle Modules + Quizzes
    if (parsedModules && parsedModules.length > 0) {
      for (const moduleData of parsedModules) {
        const newModule = new Module({
          title: moduleData.title || "Untitled Module",
          description: moduleData.description || "",
          course: course._id,
          order: moduleData.order || 0,
          content: [],
          isCompulsory:
            moduleData.isCompulsory !== undefined
              ? moduleData.isCompulsory
              : true, // Set isCompulsory from moduleData
        });

        await newModule.save();
        course.modules.push(newModule._id);

        // Module content
        if (Array.isArray(moduleData.content)) {
          for (const contentId of moduleData.content) {
            const newContentItem = contentItems.find(
              (item) => item.id === contentId
            );
            if (newContentItem) {
              const dbContentId = newContentItem.dbId;
              newModule.content.push(dbContentId);

              const contentExists = await Content.findById(dbContentId);
              if (contentExists) {
                contentExists.module = newModule._id;
                await contentExists.save();
              }
            } else if (!contentId.startsWith("temp_")) {
              try {
                const contentExists = await Content.findById(contentId);
                if (contentExists) {
                  newModule.content.push(contentId);
                  contentExists.module = newModule._id;
                  await contentExists.save();
                }
              } catch (err) {
                console.error(`Invalid content ID: ${contentId}`);
              }
            }
          }
          await newModule.save();
        }

        // Module quiz
        if (moduleData.quiz) {
          const quizData = moduleData.quiz;

          const quiz = new Quiz({
            title: quizData.title || `${newModule.title} Quiz`,
            description: quizData.description || `Quiz for ${newModule.title}`,
            course: course._id,
            questions: quizData.questions || [],
            timeLimit: quizData.timeLimit || 30,
            passingScore: quizData.passingScore || 60,
          });

          await quiz.save();
          newModule.quiz = quiz._id;
          await newModule.save();
          course.quizzes.push(quiz._id);
        }
      }

      await course.save();
    }

    // Create and send newsletter for the new course if it's not a draft
    if (!course.isDraft) {
      try {
        const { autoCreateNewsletter } = require('./newsletterController');
        await autoCreateNewsletter('course', {
          _id: course._id,
          title: course.title,
          description: course.description,
          createdBy: req.user._id
        });
      } catch (newsletterError) {
        console.error("Newsletter creation error:", newsletterError);
        // Don't fail the course creation if newsletter fails
      }
    }

    // ✅ Final Response
    res.status(201).json({
      message: "Course created successfully",
      course,
      contentItemsCreated: contentItems.map((c) => c.dbId),
      uploadedFiles: req.files.map((f) => f.fieldname),
    });
  } catch (error) {
    console.error("Course creation error:", error);
    res
      .status(500)
      .json({ message: "Error creating course", error: error.message });
  }
};

// Update a course
exports.updateCourse = async (req, res) => {
  try {
    const {
      title,
      description,
      duration,
      language,
      thumbnailUrl,
      modules,
      content,
      quizzes,
      status,
      isDraft,
    } = req.body;

    // Initialize contentItems array at the beginning to avoid reference errors
    const contentItems = [];

    // Track which temporary module IDs have already been processed to avoid duplicates
    const processedTempModuleIds = new Map();

    const course = await Course.findById(req.params.id);
    if (!course) {
      return res.status(404).json({ message: "Course not found" });
    }

    // Update fields if provided
    if (title) course.title = title;
    if (description) course.description = description;
    if (duration) course.duration = duration;
    if (language) course.language = language;

    // Handle thumbnail update
    // Check for thumbnail in req.files array since we're using upload.any()
    const thumbnailFile = req.files?.find((f) => f.fieldname === "thumbnail");
    if (thumbnailFile) {
      course.thumbnail = thumbnailFile.path;
    } else if (thumbnailUrl) {
      course.thumbnail = thumbnailUrl;
    }

    // No array fields to parse anymore

    // Always use modules
    course.hasModules = true;

    // Parse modules if provided - define at a higher scope so it's available throughout the function
    let parsedModules = [];

    if (modules) {
      try {
        parsedModules =
          typeof modules === "string" ? JSON.parse(modules) : modules;

        // Handle module updates
        if (Array.isArray(parsedModules)) {
          // Get existing module IDs
          const existingModuleIds = course.modules.map((id) => id.toString());

          // Process each module in the update
          for (const moduleData of parsedModules) {
            if (
              moduleData._id &&
              existingModuleIds.includes(moduleData._id.toString())
            ) {
              // Update existing module
              const existingModule = await Module.findById(moduleData._id);
              if (existingModule) {
                existingModule.title = moduleData.title || existingModule.title;
                existingModule.description =
                  moduleData.description || existingModule.description;
                existingModule.order =
                  moduleData.order !== undefined
                    ? moduleData.order
                    : existingModule.order;
                // Update isCompulsory if provided
                if (moduleData.isCompulsory !== undefined) {
                  existingModule.isCompulsory = moduleData.isCompulsory;
                }

                // Update content associations if provided
                if (moduleData.content && Array.isArray(moduleData.content)) {
                  // Replace temporary content IDs with database IDs
                  const updatedContent = [];

                  for (const contentId of moduleData.content) {
                    // Check if this is a newly created content item
                    const newContentItem = contentItems.find(
                      (item) => item.id === contentId
                    );

                    if (newContentItem) {
                      // Use the database ID for newly created content
                      updatedContent.push(newContentItem.dbId);
                    } else if (!contentId.startsWith("temp_")) {
                      // Only include non-temporary IDs that are valid ObjectIds
                      try {
                        // Verify this is a valid content ID
                        const contentExists = await Content.findById(contentId);
                        if (contentExists) {
                          updatedContent.push(contentId);
                        }
                      } catch (err) {
                        console.error(
                          `Error finding content with ID ${contentId}:`,
                          err.message
                        );
                        // Skip this content ID if it's invalid
                      }
                    }
                  }

                  // Update module content - ensure we only have valid ObjectIds
                  const validModuleContent = [];

                  for (const contentId of updatedContent) {
                    // Skip any non-ObjectId values
                    if (mongoose.Types.ObjectId.isValid(contentId)) {
                      validModuleContent.push(contentId);
                    }
                  }

                  existingModule.content = validModuleContent;

                  // Update content items to reference this module
                  for (const contentId of validModuleContent) {
                    try {
                      const contentExists = await Content.findById(contentId);
                      if (contentExists) {
                        contentExists.module = existingModule._id;
                        await contentExists.save();
                      }
                    } catch (err) {
                      console.error(
                        `Error updating content reference for ID ${contentId}:`,
                        err.message
                      );
                    }
                  }
                }

                // Check if the module previously had a quiz that's now removed
                const existingModuleBeforeUpdate = await Module.findById(
                  moduleData._id
                );
                if (
                  existingModuleBeforeUpdate &&
                  existingModuleBeforeUpdate.quiz &&
                  !moduleData.quiz
                ) {
                  // The quiz was removed from the module
                  const removedQuizId =
                    existingModuleBeforeUpdate.quiz.toString();

                  // Remove the quiz from the course's quizzes array
                  const quizIndex = course.quizzes.findIndex(
                    (id) => id.toString() === removedQuizId
                  );
                  if (quizIndex !== -1) {
                    course.quizzes.splice(quizIndex, 1);
                  }

                  // Delete the quiz from the database
                  await Quiz.findByIdAndDelete(removedQuizId);

                  // Update the module to remove the quiz reference
                  existingModule.quiz = null;
                }
                // Update quiz if provided
                else if (moduleData.quiz) {
                  const quizData = moduleData.quiz;

                  // Check if this is a new quiz being added during edit (has isNew flag or temp ID)
                  const isNewQuiz =
                    quizData.isNew === true ||
                    (quizData._id &&
                      quizData._id.toString().startsWith("temp_"));

                  // Check if quiz has a valid MongoDB ID (existing quiz)
                  const hasValidId =
                    quizData._id &&
                    !quizData._id.toString().startsWith("temp_") &&
                    mongoose.Types.ObjectId.isValid(quizData._id);

                  if (hasValidId && !isNewQuiz) {
                    // Update existing quiz
                    const existingQuiz = await Quiz.findById(quizData._id);
                    if (existingQuiz) {
                      existingQuiz.title = quizData.title || existingQuiz.title;
                      existingQuiz.description =
                        quizData.description || existingQuiz.description;

                      // Always update questions array if it's provided, even if it's empty
                      if (quizData.questions !== undefined) {
                        // Process questions to ensure correctAnswer is a string
                        const processedQuestions = quizData.questions.map(
                          (question) => {
                            // Ensure correctAnswer is a string
                            if (
                              question.correctAnswer !== undefined &&
                              typeof question.correctAnswer !== "string"
                            ) {
                              return {
                                ...question,
                                correctAnswer:
                                  question.correctAnswer.toString(),
                              };
                            }
                            return question;
                          }
                        );

                        existingQuiz.questions = processedQuestions;
                      }

                      existingQuiz.timeLimit =
                        quizData.timeLimit || existingQuiz.timeLimit;
                      existingQuiz.passingScore =
                        quizData.passingScore || existingQuiz.passingScore;

                      await existingQuiz.save();
                    }
                  } else {
                    // Create new quiz - either completely new or added during edit

                    // Process questions to ensure correctAnswer is a string
                    const processedQuestions = quizData.questions
                      ? quizData.questions.map((question) => {
                          // Ensure correctAnswer is a string
                          if (
                            question.correctAnswer !== undefined &&
                            typeof question.correctAnswer !== "string"
                          ) {
                            return {
                              ...question,
                              correctAnswer: question.correctAnswer.toString(),
                            };
                          }
                          return question;
                        })
                      : [];

                    const quiz = new Quiz({
                      title: quizData.title || `${existingModule.title} Quiz`,
                      description:
                        quizData.description ||
                        `Quiz for ${existingModule.title}`,
                      course: course._id,
                      questions: processedQuestions,
                      timeLimit: quizData.timeLimit || 30,
                      passingScore: quizData.passingScore || 60,
                    });

                    await quiz.save();

                    // Associate quiz with module
                    existingModule.quiz = quiz._id;

                    // Add quiz to course's quizzes array
                    if (!course.quizzes.includes(quiz._id)) {
                      course.quizzes.push(quiz._id);
                    }
                  }
                }

                await existingModule.save();
              }
            } else {
              // Check if this is a temporary module ID that we've already processed
              if (
                moduleData._id &&
                moduleData._id.toString().startsWith("temp_") &&
                processedTempModuleIds.has(moduleData._id)
              ) {
                continue;
              }

              // Create new module
              const newModule = new Module({
                title: moduleData.title || "Untitled Module",
                description: moduleData.description || "",
                course: course._id,
                order: moduleData.order || 0,
                content: [], // Initialize with empty content array
                isCompulsory:
                  moduleData.isCompulsory !== undefined
                    ? moduleData.isCompulsory
                    : true, // Set isCompulsory from moduleData
              });

              await newModule.save();

              // Add to course modules
              course.modules.push(newModule._id);

              // If this is a temporary ID, store it in our tracking map
              if (
                moduleData._id &&
                moduleData._id.toString().startsWith("temp_")
              ) {
                processedTempModuleIds.set(moduleData._id, newModule._id);
              }

              // If module has content, associate it
              if (moduleData.content && moduleData.content.length > 0) {
                // Replace temporary content IDs with database IDs
                const updatedContent = [];

                for (const contentId of moduleData.content) {
                  // Check if this is a newly created content item
                  const newContentItem = contentItems.find(
                    (item) => item.id === contentId
                  );

                  if (newContentItem) {
                    // Use the database ID for newly created content
                    updatedContent.push(newContentItem.dbId);
                  } else if (contentId.startsWith("temp_")) {
                    // This is a temporary content ID that hasn't been processed yet
                    // We'll need to create a placeholder for it and update it later

                    // Find the content details in parsedContent
                    const tempContentData = parsedContent.find(
                      (item) => item._id === contentId
                    );

                    if (tempContentData) {
                      // Create a new content item with the data from parsedContent
                      const newContent = new Content({
                        title: tempContentData.title || "New Content",
                        description: tempContentData.description || "",
                        textContent: tempContentData.textContent || "",
                        fileUrl: tempContentData.fileUrl || "",
                        creator: req.user.id,
                        status: "approved",
                        type: tempContentData.type || "document",
                        mediaType:
                          tempContentData.type === "text"
                            ? "text"
                            : tempContentData.type === "youtube"
                            ? "video"
                            : "document",
                        mimeType:
                          tempContentData.type === "text"
                            ? "text/html"
                            : tempContentData.type === "youtube"
                            ? "video/youtube"
                            : tempContentData.mimeType ||
                              "application/octet-stream",
                        module: null, // Will be updated after module is created
                      });

                      await newContent.save();

                      // Add to contentItems for tracking
                      contentItems.push({
                        id: contentId,
                        dbId: newContent._id,
                      });

                      // Add to updatedContent
                      updatedContent.push(newContent._id);
                    } else {
                      console.warn(
                        `Could not find content data for temp ID: ${contentId}`
                      );
                    }
                  } else if (!contentId.startsWith("temp_")) {
                    // Only include non-temporary IDs that are valid ObjectIds
                    try {
                      // Verify this is a valid content ID
                      const contentExists = await Content.findById(contentId);
                      if (contentExists) {
                        updatedContent.push(contentId);
                      }
                    } catch (err) {
                      console.error(
                        `Error finding content with ID ${contentId}:`,
                        err.message
                      );
                      // Skip this content ID if it's invalid
                    }
                  }
                }

                // Update module content - ensure we only have valid ObjectIds
                const validModuleContent = [];

                for (const contentId of updatedContent) {
                  // Skip any non-ObjectId values
                  if (mongoose.Types.ObjectId.isValid(contentId)) {
                    validModuleContent.push(contentId);
                  }
                }

                newModule.content = validModuleContent;

                // Update content items to reference this module
                for (const contentId of validModuleContent) {
                  try {
                    const contentExists = await Content.findById(contentId);
                    if (contentExists) {
                      contentExists.module = newModule._id;
                      await contentExists.save();
                    }
                  } catch (err) {
                    console.error(
                      `Error updating content reference for ID ${contentId}:`,
                      err.message
                    );
                  }
                }

                await newModule.save();
              }

              // If module has a quiz, create it and associate it with the module
              if (moduleData.quiz) {
                const quizData = moduleData.quiz;

                // Check if this is a temporary quiz ID that might have been processed already
                let quizId = null;

                if (
                  quizData._id &&
                  quizData._id.toString().startsWith("temp_")
                ) {
                  // Check if we've already created a quiz for this temp ID
                  const existingQuiz = await Quiz.findOne({
                    course: course._id,
                    title: quizData.title || `${newModule.title} Quiz`,
                  });

                  if (existingQuiz) {
                    quizId = existingQuiz._id;
                  }
                }

                if (!quizId) {
                  // Process questions to ensure correctAnswer is a string
                  const processedQuestions = quizData.questions
                    ? quizData.questions.map((question) => {
                        // Ensure correctAnswer is a string
                        if (
                          question.correctAnswer !== undefined &&
                          typeof question.correctAnswer !== "string"
                        ) {
                          return {
                            ...question,
                            correctAnswer: question.correctAnswer.toString(),
                          };
                        }
                        return question;
                      })
                    : [];

                  // Create a new quiz
                  const quiz = new Quiz({
                    title: quizData.title || `${newModule.title} Quiz`,
                    description:
                      quizData.description || `Quiz for ${newModule.title}`,
                    course: course._id,
                    questions: processedQuestions,
                    timeLimit: quizData.timeLimit || 30,
                    passingScore: quizData.passingScore || 60,
                  });

                  await quiz.save();
                  quizId = quiz._id;

                  // Add quiz to course's quizzes array
                  if (!course.quizzes.includes(quizId)) {
                    course.quizzes.push(quizId);
                  }
                }

                // Associate quiz with module
                newModule.quiz = quizId;
                await newModule.save();
              }
            }
          }

          // Remove modules that are no longer in the update
          const updatedModuleIds = parsedModules
            .filter((m) => m._id)
            .map((m) => m._id.toString());

          const modulesToRemove = existingModuleIds.filter(
            (id) => !updatedModuleIds.includes(id)
          );

          if (modulesToRemove.length > 0) {
            // Remove modules from course
            course.modules = course.modules.filter(
              (id) => !modulesToRemove.includes(id.toString())
            );

            // Delete the modules
            for (const moduleId of modulesToRemove) {
              // Get the module to check if it has an associated quiz
              const module = await Module.findById(moduleId);
              if (module && module.quiz) {
                // Check if the quiz exists in the course's quizzes array
                const quizId = module.quiz.toString();
                const quizIndex = course.quizzes.findIndex(
                  (id) => id.toString() === quizId
                );

                if (quizIndex !== -1) {
                  // Remove quiz from course's quizzes array using splice for direct modification
                  course.quizzes.splice(quizIndex, 1);
                }

                // Delete the quiz
                await Quiz.findByIdAndDelete(module.quiz);
              }

              // Update content items to remove module reference
              await Content.updateMany(
                { module: moduleId },
                { $unset: { module: "" } }
              );

              // Delete the module
              await Module.findByIdAndDelete(moduleId);
            }
          }
        }
      } catch (err) {
        console.error("Error processing modules update:", err);
      }
    }

    // Process content files if they exist
    const contentFileIds = req.body.contentFileIds
      ? Array.isArray(req.body.contentFileIds)
        ? req.body.contentFileIds
        : [req.body.contentFileIds]
      : [];

    // Parse content if it's a string
    let parsedContent;
    try {
      parsedContent =
        typeof content === "string" ? JSON.parse(content) : content || [];
    } catch (err) {
      console.error("Error parsing content JSON:", err);
      parsedContent = [];
    }

    // We already have contentItems array initialized at the top of the function

    // Get content module mappings from request
    const contentModules = {};
    if (req.body.contentModules) {
      // Handle array format
      if (Array.isArray(req.body.contentModules)) {
        req.body.contentModules.forEach((moduleId, index) => {
          if (req.body.contentFileIds && req.body.contentFileIds[index]) {
            contentModules[req.body.contentFileIds[index]] = moduleId;
          }
        });
      }
      // Handle object format from formData
      else {
        Object.keys(req.body).forEach((key) => {
          if (key.startsWith("contentModules[")) {
            const match = key.match(/\[(\d+)\]/);
            if (match && match[1]) {
              const index = parseInt(match[1]);
              if (
                req.body.contentFileIds &&
                req.body[`contentFileIds[${index}]`]
              ) {
                const contentId = req.body[`contentFileIds[${index}]`];
                const moduleId = req.body[key];
                contentModules[contentId] = moduleId;
              }
            }
          }
        });
      }
    }

    // Process content files if any
    if (
      req.files &&
      req.files.length > 0 &&
      contentFileIds &&
      contentFileIds.length > 0
    ) {
      for (let i = 0; i < req.files.length; i++) {
        const file = req.files[i];
        // Skip the thumbnail file
        if (file.fieldname === "thumbnail") continue;

        // Extract the index from the fieldname (contentFiles[0], contentFiles[1], etc.)
        const fieldnameParts = file.fieldname.match(/\[(\d+)\]/);
        if (!fieldnameParts) {
          continue;
        }

        const index = parseInt(fieldnameParts[1]);
        const contentId = contentFileIds[index];

        if (!contentId) {
          console.warn(`Missing content ID for file index ${index}`);
          continue;
        }

        // Find the content details in the content array
        const contentItem = parsedContent.find(
          (item) => item._id === contentId
        );
        if (!contentItem) {
          console.warn(`Content item not found for ID: ${contentId}`);
          continue;
        }

        let mediaType = "document";
        let mimeType = file.mimetype;
        const fileExt = file.originalname.split(".").pop().toLowerCase();

        if (["mp4", "mov", "avi", "mkv"].includes(fileExt)) {
          mediaType = "video";
        } else if (["jpg", "jpeg", "png", "gif", "webp"].includes(fileExt)) {
          mediaType = "image";
        }

        // Get module ID from either content item or contentModules mapping
        let moduleId = contentItem.module || null;

        // If we have a module mapping for this content, use it
        if (contentModules[contentId]) {
          moduleId = contentModules[contentId];

          // Check if this is a temporary module ID and create a new module if needed
          if (moduleId && moduleId.toString().startsWith("temp_")) {
            // Check if we've already processed this temporary module ID
            if (processedTempModuleIds.has(moduleId)) {
              // Use the previously created module instead of creating a new one
              moduleId = processedTempModuleIds.get(moduleId);
            } else {
              // Find the module data in parsedModules
              const tempModuleData = parsedModules.find(
                (m) => m._id === moduleId
              );

              // Create default module data if we can't find it in parsedModules
              const moduleDataToUse = tempModuleData || {
                title: "New Module",
                description: "",
                order: course.modules ? course.modules.length : 0,
                isCompulsory: true,
              };

              // Create a new module with proper MongoDB ID
              const newModule = new Module({
                title: moduleDataToUse.title || "New Module",
                description: moduleDataToUse.description || "",
                course: course._id,
                order: moduleDataToUse.order || 0,
                content: [], // Initialize with empty content array
                isCompulsory:
                  moduleDataToUse.isCompulsory !== undefined
                    ? moduleDataToUse.isCompulsory
                    : true,
              });

              await newModule.save();

              // Add to course modules
              course.modules.push(newModule._id);

              // Store the mapping from temp ID to real ID
              processedTempModuleIds.set(moduleId, newModule._id);

              // Update moduleId to use the new MongoDB ID
              moduleId = newModule._id;
            }
          }
        }

        // Check if this is an existing content item being updated
        if (!contentId.startsWith("temp_")) {
          try {
            // This is an existing content - update it instead of creating a new one
            const existingContent = await Content.findById(contentId);

            if (existingContent) {
              // Update the content properties
              existingContent.title = contentItem.title;
              existingContent.description = contentItem.description;
              existingContent.fileUrl = file.path;
              existingContent.type =
                contentItem.type ||
                (mediaType === "video"
                  ? "video"
                  : mediaType === "image"
                  ? "image"
                  : "document");
              existingContent.mediaType = mediaType;
              existingContent.mimeType = mimeType;
              existingContent.size = file.size;

              // Update module if needed
              if (
                moduleId &&
                (!existingContent.module ||
                  existingContent.module.toString() !== moduleId.toString())
              ) {
                existingContent.module = moduleId;

                // Add to the new module if not already there
                try {
                  const module = await Module.findById(moduleId);
                  if (module && !module.content.includes(existingContent._id)) {
                    module.content.push(existingContent._id);
                    await module.save();
                  }
                } catch (err) {
                  console.error(
                    `Error adding content to module ${moduleId}:`,
                    err
                  );
                }
              }

              await existingContent.save();

              // No need to add to contentItems as we're not creating a new item
              continue;
            }
          } catch (err) {
            console.error(`Error updating existing content ${contentId}:`, err);
            // If there's an error updating, fall back to creating a new content
          }
        }

        // Create a new content item (for new content or if update failed)
        // Validate moduleId is a valid ObjectId before using it
        const validModuleId =
          moduleId && mongoose.Types.ObjectId.isValid(moduleId)
            ? moduleId
            : null;
        if (moduleId && !validModuleId) {
          console.warn(
            `Invalid module ID ${moduleId} for content ${contentId}, setting to null`
          );
        }

        // Check if we already created a placeholder for this content
        const existingPlaceholder = contentItems.find(
          (item) => item.id === contentId
        );
        let newContent;

        if (existingPlaceholder) {
          // Update the existing placeholder with the file information
          newContent = await Content.findById(existingPlaceholder.dbId);

          if (newContent) {
            newContent.title = contentItem.title;
            newContent.description = contentItem.description;
            newContent.fileUrl = file.path;
            newContent.type =
              contentItem.type ||
              (mediaType === "video"
                ? "video"
                : mediaType === "image"
                ? "image"
                : "document");
            newContent.mediaType = mediaType;
            newContent.mimeType = mimeType;
            newContent.size = file.size;
            newContent.module = validModuleId;

            await newContent.save();

            // No need to add to contentItems as it's already there
            // But we need to continue with the rest of the processing
          } else {
            console.warn(
              `Could not find placeholder content with ID ${existingPlaceholder.dbId}`
            );
            // Fall back to creating a new content item
            newContent = null;
          }
        }

        // If we didn't find or couldn't update a placeholder, create a new content item
        if (!existingPlaceholder || !newContent) {
          newContent = new Content({
            title: contentItem.title,
            description: contentItem.description,
            fileUrl: file.path,
            creator: req.user.id,
            status: "approved",
            type:
              contentItem.type ||
              (mediaType === "video"
                ? "video"
                : mediaType === "image"
                ? "image"
                : "document"),
            mediaType,
            mimeType,
            size: file.size,
            module: validModuleId,
          });

          await newContent.save();

          // Add to contentItems for tracking
          contentItems.push({
            id: contentId,
            dbId: newContent._id,
          });
        }

        // If we have a valid module ID, add this content to the module
        if (validModuleId) {
          try {
            const module = await Module.findById(validModuleId);
            if (module) {
              module.content.push(newContent._id);
              await module.save();
            }
          } catch (err) {
            console.error(
              `Error adding content to module ${validModuleId}:`,
              err
            );
          }
        }
      }
    }

    // Update course content
    if (content) {
      try {
        // First, process all existing content items to update title and description
        // This ensures all content types (document, video, image, text, youtube) get updated
        const existingContentPromises = parsedContent
          .filter((item) => !item._id.startsWith("temp_")) // Only process existing content
          .map(async (item) => {
            try {
              const existingContent = await Content.findById(item._id);
              if (existingContent) {
                // Always update title and description
                existingContent.title = item.title;
                existingContent.description = item.description;

                // For text and YouTube content, also update textContent
                if (
                  (item.type === "text" || item.type === "youtube") &&
                  item.textContent
                ) {
                  existingContent.textContent = item.textContent;

                  // For YouTube content, also update fileUrl for backward compatibility
                  if (item.type === "youtube") {
                    existingContent.fileUrl = item.textContent;
                    existingContent.type = "youtube";
                    existingContent.mediaType = "video";
                    existingContent.mimeType = "video/youtube";
                  }
                }

                await existingContent.save();
              }
            } catch (err) {
              console.error(
                `Error updating existing content ${item._id}:`,
                err
              );
            }
            return null;
          });

        // Process new text and YouTube content items that don't require file uploads
        const newTextContentPromises = parsedContent
          .filter(
            (item) =>
              item._id.startsWith("temp_") &&
              (item.type === "text" || item.type === "youtube")
          )
          .map(async (item) => {
            try {
              // Get module ID from contentModules mapping
              let moduleId = item.module || null;

              // If we have a module mapping for this content, use it
              if (contentModules[item._id]) {
                moduleId = contentModules[item._id];
              }

              // Check if this is a temporary module ID and get the real one
              if (moduleId && moduleId.toString().startsWith("temp_")) {
                if (processedTempModuleIds.has(moduleId)) {
                  const realModuleId = processedTempModuleIds.get(moduleId);
                  moduleId = realModuleId;
                } else {
                  moduleId = null;
                }
              }

              // Validate moduleId is a valid ObjectId before using it
              const validModuleId =
                moduleId && mongoose.Types.ObjectId.isValid(moduleId)
                  ? moduleId
                  : null;

              // Create the content item
              const newContent = new Content({
                title: item.title,
                description: item.description,
                textContent: item.textContent || "",
                fileUrl: item.type === "youtube" ? item.textContent : "",
                creator: req.user.id,
                status: "approved",
                type: item.type,
                mediaType: item.type === "youtube" ? "video" : "text",
                mimeType:
                  item.type === "youtube" ? "video/youtube" : "text/html",
                module: validModuleId,
              });

              await newContent.save();

              // Add to contentItems for tracking
              contentItems.push({
                id: item._id,
                dbId: newContent._id,
              });

              // If we have a valid module ID, add this content to the module
              if (validModuleId) {
                try {
                  const module = await Module.findById(validModuleId);
                  if (module) {
                    module.content.push(newContent._id);
                    await module.save();
                  }
                } catch (err) {
                  console.error(
                    `Error adding ${item.type} content to module ${validModuleId}:`,
                    err
                  );
                }
              }
            } catch (err) {
              console.error(`Error creating new ${item.type} content:`, err);
            }
            return null;
          });

        // Wait for all content updates and creations to complete
        await Promise.all([
          ...existingContentPromises,
          ...newTextContentPromises,
        ]);

        // Process text and YouTube content items separately - these don't need file uploads
        // This is for creating new text/YouTube content items
        const textContentPromises = parsedContent
          .filter(
            (item) =>
              (item.type === "text" || item.type === "youtube") &&
              item._id.startsWith("temp_")
          )
          .map(async (item) => {
            // If it's not a temp item (already exists in DB), skip creation
            if (!item._id.startsWith("temp_")) {
              return null;
            }

            // Get module ID from either content item or contentModules mapping
            let moduleId = item.module || null;

            // If this is a temp ID, check if we have a module mapping for it
            if (item._id.startsWith("temp_") && contentModules[item._id]) {
              moduleId = contentModules[item._id];

              // Check if this is a temporary module ID and create a new module if needed
              if (moduleId && moduleId.toString().startsWith("temp_")) {
                // Check if we've already processed this temporary module ID
                if (processedTempModuleIds.has(moduleId)) {
                  // Use the previously created module instead of creating a new one
                  moduleId = processedTempModuleIds.get(moduleId);
                } else {
                  // Find the module data in parsedModules
                  const tempModuleData = parsedModules.find(
                    (m) => m._id === moduleId
                  );

                  // Create default module data if we can't find it in parsedModules
                  const moduleDataToUse = tempModuleData || {
                    title: "New Module",
                    description: "",
                    order: course.modules ? course.modules.length : 0,
                    isCompulsory: true,
                  };

                  // Create a new module with proper MongoDB ID
                  const newModule = new Module({
                    title: moduleDataToUse.title || "New Module",
                    description: moduleDataToUse.description || "",
                    course: course._id,
                    order: moduleDataToUse.order || 0,
                    content: [], // Initialize with empty content array
                    isCompulsory:
                      moduleDataToUse.isCompulsory !== undefined
                        ? moduleDataToUse.isCompulsory
                        : true,
                  });

                  await newModule.save();

                  // Add to course modules
                  course.modules.push(newModule._id);

                  // Store the mapping from temp ID to real ID
                  processedTempModuleIds.set(moduleId, newModule._id);

                  // Update moduleId to use the new MongoDB ID
                  moduleId = newModule._id;
                }
              }
            }

            // Handle new YouTube content
            if (item.type === "youtube") {
              // Validate moduleId is a valid ObjectId before using it
              const validModuleId =
                moduleId && mongoose.Types.ObjectId.isValid(moduleId)
                  ? moduleId
                  : null;
              if (moduleId && !validModuleId) {
                console.warn(
                  `Invalid module ID ${moduleId} for YouTube content ${item._id}, setting to null`
                );
              }

              const newYoutubeContent = new Content({
                title: item.title,
                description: item.description,
                textContent: item.textContent || "",
                fileUrl: item.textContent || "", // Store YouTube URL in fileUrl for backward compatibility
                creator: req.user.id,
                status: "approved",
                type: "youtube",
                mediaType: "video",
                mimeType: "video/youtube",
                module: validModuleId,
              });

              await newYoutubeContent.save();

              // If we have a valid module ID, add this content to the module
              if (validModuleId) {
                try {
                  const module = await Module.findById(validModuleId);
                  if (module) {
                    module.content.push(newYoutubeContent._id);
                    await module.save();
                  }
                } catch (err) {
                  console.error(
                    `Error adding YouTube content to module ${validModuleId}:`,
                    err
                  );
                }
              }

              return {
                id: item._id,
                dbId: newYoutubeContent._id,
              };
            }

            // Create new text content
            // Validate moduleId is a valid ObjectId before using it
            const validModuleId =
              moduleId && mongoose.Types.ObjectId.isValid(moduleId)
                ? moduleId
                : null;
            if (moduleId && !validModuleId) {
              console.warn(
                `Invalid module ID ${moduleId} for text content ${item._id}, setting to null`
              );
            }

            const newTextContent = new Content({
              title: item.title,
              description: item.description,
              textContent: item.textContent || "",
              creator: req.user.id,
              status: "approved",
              type: "text",
              mediaType: "text",
              mimeType: "text/html",
              module: validModuleId,
            });

            await newTextContent.save();

            // If we have a valid module ID, add this content to the module
            if (validModuleId) {
              try {
                const module = await Module.findById(validModuleId);
                if (module) {
                  module.content.push(newTextContent._id);
                  await module.save();
                }
              } catch (err) {
                console.error(
                  `Error adding text content to module ${validModuleId}:`,
                  err
                );
              }
            }

            return {
              id: item._id,
              dbId: newTextContent._id,
            };
          });

        // Filter out null values from items that don't need creation
        const textContentItemsResults = await Promise.all(textContentPromises);
        const textContentItems = textContentItemsResults.filter(
          (item) => item !== null
        );

        contentItems.push(...textContentItems);

        // Replace temporary IDs with database IDs for newly created content
        if (contentItems.length > 0) {
          // Filter out content items that have been replaced with new uploads
          parsedContent = parsedContent.filter(
            (item) =>
              !contentItems.some(
                (newItem) => newItem && newItem.id === item._id
              )
          );

          // Add the newly created content items
          // Make sure we're only storing valid ObjectIds in the course.content array
          const validContentIds = [];

          // Add existing content IDs (filter out any objects or invalid IDs)
          if (Array.isArray(parsedContent)) {
            for (const item of parsedContent) {
              // Skip temporary content items
              if (item._id && item._id.toString().startsWith("temp_")) {
                continue;
              }

              // If it's a valid ObjectId, add it
              if (item._id && mongoose.Types.ObjectId.isValid(item._id)) {
                validContentIds.push(item._id);
              }
            }
          }

          // Add newly created content IDs
          if (contentItems && contentItems.length > 0) {
            for (const item of contentItems) {
              if (item && item.dbId) {
                validContentIds.push(item.dbId);
              }
            }
          }

          // Set the course content to the valid IDs
          course.content = validContentIds;
        } else {
          // If no new content items, make sure we're still only storing valid ObjectIds
          const validContentIds = [];

          if (Array.isArray(parsedContent)) {
            for (const item of parsedContent) {
              // Skip temporary content items
              if (item._id && item._id.toString().startsWith("temp_")) {
                continue;
              }

              // If it's a valid ObjectId, add it
              if (item._id && mongoose.Types.ObjectId.isValid(item._id)) {
                validContentIds.push(item._id);
              }
            }
          }

          course.content = validContentIds;
        }
      } catch (err) {
        console.error("Error processing content update:", err);
      }
    }

    if (quizzes) {
      try {
        // Parse quizzes if it's a string
        const parsedQuizzes =
          typeof quizzes === "string" ? JSON.parse(quizzes) : quizzes;

        // Process each quiz to update its questions in the database
        if (Array.isArray(parsedQuizzes)) {
          // Filter out temporary quiz IDs - these are handled in the module processing section
          const validQuizzes = [];

          for (const quizData of parsedQuizzes) {
            // Skip temporary quizzes - they are handled in the module processing
            if (quizData._id && quizData._id.toString().startsWith("temp_")) {
              // Check if this quiz is associated with a module
              if (quizData.module) {
                // Check if the module ID is a temporary ID
                if (quizData.module.toString().startsWith("temp_")) {
                  // Check if we've already processed this temporary module ID
                  if (processedTempModuleIds.has(quizData.module)) {
                    const realModuleId = processedTempModuleIds.get(
                      quizData.module
                    );

                    try {
                      // Find the real module
                      const module = await Module.findById(realModuleId);
                      if (module) {
                        // If the module doesn't have a quiz yet, create one
                        if (!module.quiz) {
                          // Process questions to ensure correctAnswer is a string
                          const processedQuestions = quizData.questions
                            ? quizData.questions.map((question) => {
                                // Ensure correctAnswer is a string
                                if (
                                  question.correctAnswer !== undefined &&
                                  typeof question.correctAnswer !== "string"
                                ) {
                                  return {
                                    ...question,
                                    correctAnswer:
                                      question.correctAnswer.toString(),
                                  };
                                }
                                return question;
                              })
                            : [];

                          // Create a new quiz
                          const quiz = new Quiz({
                            title: quizData.title || `${module.title} Quiz`,
                            description:
                              quizData.description ||
                              `Quiz for ${module.title}`,
                            course: course._id,
                            questions: processedQuestions,
                            timeLimit: quizData.timeLimit || 30,
                            passingScore: quizData.passingScore || 60,
                          });

                          await quiz.save();

                          // Associate quiz with module
                          module.quiz = quiz._id;
                          await module.save();

                          // Add quiz to course's quizzes array
                          if (!course.quizzes.includes(quiz._id)) {
                            course.quizzes.push(quiz._id);
                          }
                        }
                      }
                    } catch (err) {
                      console.error(
                        `Error processing temporary quiz ${quizData._id} for real module ${realModuleId}:`,
                        err
                      );
                    }
                  }
                } else {
                  // This is a real module ID
                  try {
                    // Find the module
                    const module = await Module.findById(quizData.module);
                    if (module) {
                      // If the module doesn't have a quiz yet, create one
                      if (!module.quiz) {
                        // Process questions to ensure correctAnswer is a string
                        const processedQuestions = quizData.questions
                          ? quizData.questions.map((question) => {
                              // Ensure correctAnswer is a string
                              if (
                                question.correctAnswer !== undefined &&
                                typeof question.correctAnswer !== "string"
                              ) {
                                return {
                                  ...question,
                                  correctAnswer:
                                    question.correctAnswer.toString(),
                                };
                              }
                              return question;
                            })
                          : [];

                        // Create a new quiz
                        const quiz = new Quiz({
                          title: quizData.title || `${module.title} Quiz`,
                          description:
                            quizData.description || `Quiz for ${module.title}`,
                          course: course._id,
                          questions: processedQuestions,
                          timeLimit: quizData.timeLimit || 30,
                          passingScore: quizData.passingScore || 60,
                        });

                        await quiz.save();

                        // Associate quiz with module
                        module.quiz = quiz._id;
                        await module.save();

                        // Add quiz to course's quizzes array
                        if (!course.quizzes.includes(quiz._id)) {
                          course.quizzes.push(quiz._id);
                        }
                      }
                    }
                  } catch (err) {
                    console.error(
                      `Error processing temporary quiz ${quizData._id} for module ${quizData.module}:`,
                      err
                    );
                  }
                }
              }

              continue;
            }

            // Only process quizzes with a valid MongoDB ID
            if (quizData._id && mongoose.Types.ObjectId.isValid(quizData._id)) {
              try {
                const existingQuiz = await Quiz.findById(quizData._id);
                if (existingQuiz) {
                  // Update quiz properties
                  existingQuiz.title = quizData.title || existingQuiz.title;
                  existingQuiz.description =
                    quizData.description || existingQuiz.description;

                  // Always update questions array if it's provided, even if it's empty
                  if (quizData.questions !== undefined) {
                    // Process questions to ensure correctAnswer is a string
                    const processedQuestions = quizData.questions.map(
                      (question) => {
                        // Ensure correctAnswer is a string
                        if (
                          question.correctAnswer !== undefined &&
                          typeof question.correctAnswer !== "string"
                        ) {
                          return {
                            ...question,
                            correctAnswer: question.correctAnswer.toString(),
                          };
                        }
                        return question;
                      }
                    );

                    existingQuiz.questions = processedQuestions;
                  }

                  existingQuiz.timeLimit =
                    quizData.timeLimit || existingQuiz.timeLimit;
                  existingQuiz.passingScore =
                    quizData.passingScore || existingQuiz.passingScore;

                  // Save the updated quiz
                  await existingQuiz.save();
                  validQuizzes.push(existingQuiz._id);
                }
              } catch (err) {
                console.error(`Error processing quiz ${quizData._id}:`, err);
              }
            }
          }

          // Only update course.quizzes with valid quiz IDs
          // Don't overwrite the entire array as it may contain quizzes created in the module processing
          if (validQuizzes.length > 0) {
            // Merge existing quizzes with valid quizzes from the quizzes array
            const existingQuizIds = course.quizzes.map((id) => id.toString());
            for (const quizId of validQuizzes) {
              if (!existingQuizIds.includes(quizId.toString())) {
                course.quizzes.push(quizId);
              }
            }
          }
        }
      } catch (err) {
        console.error("Error processing quizzes update:", err);
      }
    }

    // Handle status and draft status update if provided
    if (status !== undefined) {
      course.status = Number(status);
    }

    if (isDraft !== undefined) {
      course.isDraft = isDraft === "true" || isDraft === true;
    }

    // Save the updated course
    await course.save();
    res.json(course);
  } catch (error) {
    console.error("Course update error:", error);
    res
      .status(500)
      .json({ message: "Error updating course", error: error.message });
  }
};

// Delete a course (soft delete)
exports.deleteCourse = async (req, res) => {
  try {
    const course = await Course.findById(req.params.id);
    if (!course) {
      return res.status(404).json({ message: "Course not found" });
    }

    // Soft delete - update status to -1 (deleted)
    course.status = -1;
    await course.save();

    res.json({ message: "Course deleted successfully" });
  } catch (error) {
    res
      .status(500)
      .json({ message: "Error deleting course", error: error.message });
  }
};

// Add content to a course
exports.addContentToCourse = async (req, res) => {
  try {
    const { courseId, contentId } = req.body;

    const course = await Course.findById(courseId);
    if (!course) {
      return res.status(404).json({ message: "Course not found" });
    }

    // Check if content exists
    const content = await Content.findById(contentId);
    if (!content) {
      return res.status(404).json({ message: "Content not found" });
    }

    // Add content to course if not already added
    if (!course.content.includes(contentId)) {
      course.content.push(contentId);
      await course.save();
    }

    res.json(course);
  } catch (error) {
    res.status(500).json({
      message: "Error adding content to course",
      error: error.message,
    });
  }
};

// Add quiz to a course
exports.addQuizToCourse = async (req, res) => {
  try {
    const { courseId, quizId } = req.body;

    const course = await Course.findById(courseId);
    if (!course) {
      return res.status(404).json({ message: "Course not found" });
    }

    // Check if quiz is already in the course
    if (!course.quizzes.includes(quizId)) {
      course.quizzes.push(quizId);
      await course.save();
    }

    res.json(course);
  } catch (error) {
    res
      .status(500)
      .json({ message: "Error adding quiz to course", error: error.message });
  }
};

// Create a quiz
exports.createQuiz = async (req, res) => {
  try {
    const {
      title,
      description,
      courseId,
      questions,
      timeLimit,
      passingScore,
      moduleRequirement
    } = req.body;

    // Check if course exists
    const course = await Course.findById(courseId);
    if (!course) {
      return res.status(404).json({ message: "Course not found" });
    }

    // Process questions to support new weighted format
    const processedQuestions = questions ? questions.map(question => {
      // If question has the new options format with weights
      if (question.options && Array.isArray(question.options) &&
          question.options.some(opt => typeof opt === 'object' && opt.hasOwnProperty('text'))) {
        return {
          ...question,
          options: question.options.map(opt => ({
            text: opt.text || opt,
            weight: opt.weight || 0,
            isCorrect: opt.isCorrect || false
          }))
        };
      } else {
        // Legacy format - convert to new format
        const options = question.options || [];
        const correctAnswerIndex = parseInt(question.correctAnswer) || 0;

        return {
          ...question,
          options: options.map((optionText, index) => ({
            text: optionText,
            weight: index === correctAnswerIndex ? 100 : 0,
            isCorrect: index === correctAnswerIndex
          })),
          questionType: question.questionType || 'single-answer'
        };
      }
    }) : [];

    const Quiz = require("../models/Quiz");
    const quiz = new Quiz({
      title,
      description,
      course: courseId,
      questions: processedQuestions,
      timeLimit: timeLimit || 30,
      passingScore: passingScore || 60,
      moduleRequirement: moduleRequirement || {
        isRequired: true,
        allowRetakes: true,
        maxAttempts: -1
      }
    });

    await quiz.save();

    // Add quiz to course if not added automatically
    if (!course.quizzes.includes(quiz._id)) {
      course.quizzes.push(quiz._id);
      await course.save();
    }

    res.status(201).json(quiz);
  } catch (error) {
    res
      .status(500)
      .json({ message: "Error creating quiz", error: error.message });
  }
};

// Update a quiz
exports.updateQuiz = async (req, res) => {
  try {
    const { title, description, questions, timeLimit, passingScore } = req.body;
    const { quizId } = req.params;

    const Quiz = require("../models/Quiz");
    const quiz = await Quiz.findById(quizId);
    if (!quiz) {
      return res.status(404).json({ message: "Quiz not found" });
    }

    quiz.title = title || quiz.title;
    quiz.description = description || quiz.description;
    if (questions) quiz.questions = questions;
    if (timeLimit) quiz.timeLimit = timeLimit;
    if (passingScore) quiz.passingScore = passingScore;

    await quiz.save();
    res.json(quiz);
  } catch (error) {
    res
      .status(500)
      .json({ message: "Error updating quiz", error: error.message });
  }
};

// Get all quizzes
exports.getQuizzes = async (_, res) => {
  try {
    const Quiz = require("../models/Quiz");
    const quizzes = await Quiz.find().populate("course", "title");

    res.json(quizzes);
  } catch (error) {
    res
      .status(500)
      .json({ message: "Error retrieving quizzes", error: error.message });
  }
};

// Get a specific quiz
exports.getQuiz = async (req, res) => {
  try {
    const { quizId } = req.params;
    const Quiz = require("../models/Quiz");
    const quiz = await Quiz.findById(quizId).populate("course", "title");

    if (!quiz) {
      return res.status(404).json({ message: "Quiz not found" });
    }

    res.json(quiz);
  } catch (error) {
    res
      .status(500)
      .json({ message: "Error retrieving quiz", error: error.message });
  }
};

exports.updateProfile = async (req, res) => {
  const { name, phone, address } = req.body;
  const user = await User.findById(req.user.id);
  user.name = name || user.name;
  user.profile = { phone, address };
  await user.save();
  res.json(user);
};
exports.updatePassword = async (req, res) => {
  const { currentPassword, newPassword, confirmPassword } = req.body;

  // Validate passwords match
  if (newPassword !== confirmPassword) {
    return res.status(400).json({ msg: "New passwords do not match" });
  }

  const user = await User.findById(req.user.id);
  if (!user) {
    return res.status(404).json({ msg: "User not found" });
  }

  const isMatch = await bcrypt.compare(currentPassword, user.password);
  if (!isMatch) {
    return res.status(400).json({ msg: "Invalid current password" });
  }

  user.password = await bcrypt.hash(newPassword, 12);
  await user.save();
  res.json({ msg: "Password updated" });
};

// Get all users (admin, university, educator)
exports.getAllUsers = async (_, res) => {
  try {
    // Return all users regardless of status
    const users = await User.find().select("-password");
    res.json(users);
  } catch (error) {
    res
      .status(500)
      .json({ message: "Error retrieving users", error: error.message });
  }
};
// Get all educators
exports.getAllEducators = async (_, res) => {
  try {
    // Return all educators regardless of status or university
    const educators = await User.find({ role: "educator" })
      .populate("university", "name category")
      .populate("roleRef", "name") // Populate the role reference
      .select("-password");
    res.json(educators);
  } catch (error) {
    res
      .status(500)
      .json({ message: "Error retrieving educators", error: error.message });
  }
};

// Create educator
exports.createEducator = async (req, res, next) => {
  try {
    const {
      email,
      firstName,
      lastName,
      roleId,
      phoneNumber,
      address,
      zipcode,
      state,
      category,
      schoolName,
    } = req.body;

    // Prepare profile object
    const profile = {
      address,
      zipcode,
      state,
      category,
      schoolName,
      socialLinks: {},
    };

    // Add avatar if profile image was uploaded
    if (req.file) {
      profile.avatar = `uploads/profiles/${req.file.filename}`;
    }

    // We always use "educator" as the core role value
    // The roleId only updates the roleRef field for permissions
    const educator = new User({
      email,
      role: "educator", // Fixed core role value
      firstName,
      lastName,
      name: `${firstName} ${lastName}`,
      phoneNumber,
      roleRef: roleId || undefined, // Assign role if provided
      profile,
    });

    await educator.save();
    res.json(educator);
  } catch (error) {
    next(error);
  }
};

// Get educator by ID
exports.getEducatorById = async (req, res) => {
  try {
    const educator = await User.findOne({
      _id: req.params.id,
      role: "educator",
    })
      .populate("roleRef", "name") // Populate the role reference
      .select("-password");

    if (!educator) {
      return res.status(404).json({ message: "Educator not found" });
    }

    res.json(educator);
  } catch (error) {
    res
      .status(500)
      .json({ message: "Error retrieving educator", error: error.message });
  }
};

// Update educator
exports.updateEducator = async (req, res) => {
  try {
    const {
      name,
      email,
      phoneNumber,
      address,
      zipcode,
      state,
      status,
      category,
      schoolName,
      roleId,
    } = req.body;
    const educator = await User.findById(req.params.id);

    if (!educator) {
      return res.status(404).json({ message: "Educator not found" });
    }

    // Only update fields that are provided
    if (name) educator.name = name;
    if (email) educator.email = email;
    if (phoneNumber) educator.phoneNumber = phoneNumber;
    if (status !== undefined) educator.status = Number(status);

    // Update only roleRef if roleId is provided
    if (roleId) {
      educator.roleRef = roleId;
      // Note: We no longer update the core role field, it remains fixed as "educator"
    }

    // Initialize profile if it doesn't exist
    if (!educator.profile) {
      educator.profile = {};
    }

    // Update profile fields if provided
    if (address) educator.profile.address = address;
    if (zipcode) educator.profile.zipcode = zipcode;
    if (state) educator.profile.state = state;
    if (category) educator.profile.category = category;
    if (schoolName) educator.profile.schoolName = schoolName;

    // Handle profile image upload
    if (req.file) {
      educator.profile.avatar = `uploads/profiles/${req.file.filename}`;
    }

    await educator.save();
    res.json(educator);
  } catch (error) {
    res
      .status(500)
      .json({ message: "Error updating educator", error: error.message });
  }
};
