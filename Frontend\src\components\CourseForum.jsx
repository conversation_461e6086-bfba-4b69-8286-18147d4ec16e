import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { 
  FaComments, 
  FaPlus, 
  FaThumbsUp, 
  FaReply, 
  FaEye, 
  FaQuestion,
  FaExclamation,
  FaCheckCircle,
  FaTimes,
  FaEdit,
  FaTrash
} from 'react-icons/fa';
import '../assets/styles/CourseForum.css';
import { toast } from 'react-toastify';

const VITE_IMAGE_URL = import.meta.env.VITE_IMAGE_URL;

const CourseForum = ({ courseId }) => {
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.auth);
  
  const [forum, setForum] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showNewThreadForm, setShowNewThreadForm] = useState(false);
  const [selectedThread, setSelectedThread] = useState(null);
  const [replyingTo, setReplyingTo] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('recent');

  // Form states
  const [newThread, setNewThread] = useState({
    title: '',
    content: '',
    isQuestion: false,
    category: 'general',
    tags: ''
  });
  const [replyContent, setReplyContent] = useState('');

  // Forum categories
  const categories = [
    { id: 'all', name: 'All Discussions', icon: FaComments },
    { id: 'general', name: 'General Discussion', icon: FaComments },
    { id: 'help', name: 'Help & Support', icon: FaQuestion },
    { id: 'qa', name: 'Q&A', icon: FaExclamation }
  ];

  useEffect(() => {
    if (courseId) {
      fetchForum();
    }
  }, [courseId]);

  const fetchForum = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('accessToken');
      const response = await fetch(`${import.meta.env.VITE_API_URL}/forum/course/${courseId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setForum(data);
      } else {
        throw new Error('Failed to fetch forum');
      }
    } catch (error) {
      console.error('Error fetching forum:', error);
      toast.error('Failed to load forum');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateThread = async (e) => {
    e.preventDefault();
    
    if (!newThread.title.trim() || !newThread.content.trim()) {
      toast.error('Title and content are required');
      return;
    }

    try {
      const token = localStorage.getItem('accessToken');
      const response = await fetch(`${import.meta.env.VITE_API_URL}/forum/course/${courseId}/thread`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          title: newThread.title.trim(),
          content: newThread.content.trim(),
          isQuestion: newThread.isQuestion,
          category: newThread.category,
          tags: newThread.tags.split(',').map(tag => tag.trim()).filter(tag => tag)
        })
      });

      if (response.ok) {
        const data = await response.json();
        setForum(data.forum);
        setNewThread({ title: '', content: '', isQuestion: false, category: 'general', tags: '' });
        setShowNewThreadForm(false);
        toast.success('Thread created successfully');
      } else {
        throw new Error('Failed to create thread');
      }
    } catch (error) {
      console.error('Error creating thread:', error);
      toast.error('Failed to create thread');
    }
  };

  const handleAddReply = async (threadId) => {
    if (!replyContent.trim()) {
      toast.error('Reply content is required');
      return;
    }

    try {
      const token = localStorage.getItem('accessToken');
      const response = await fetch(`${import.meta.env.VITE_API_URL}/forum/course/${courseId}/thread/${threadId}/reply`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          content: replyContent.trim(),
          isAnswer: false
        })
      });

      if (response.ok) {
        const data = await response.json();
        setForum(data.forum);
        setReplyContent('');
        setReplyingTo(null);
        toast.success('Reply added successfully');
      } else {
        throw new Error('Failed to add reply');
      }
    } catch (error) {
      console.error('Error adding reply:', error);
      toast.error('Failed to add reply');
    }
  };

  const handleLikeThread = async (threadId) => {
    try {
      const token = localStorage.getItem('accessToken');
      const response = await fetch(`${import.meta.env.VITE_API_URL}/forum/course/${courseId}/thread/${threadId}/like`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        await fetchForum(); // Refresh forum data
      } else {
        throw new Error('Failed to toggle like');
      }
    } catch (error) {
      console.error('Error toggling like:', error);
      toast.error('Failed to toggle like');
    }
  };

  const incrementViews = async (threadId) => {
    try {
      const token = localStorage.getItem('accessToken');
      await fetch(`${import.meta.env.VITE_API_URL}/forum/course/${courseId}/thread/${threadId}/view`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
    } catch (error) {
      console.error('Error incrementing views:', error);
    }
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getUserDisplayName = (user) => {
    if (user.firstName && user.lastName) {
      return `${user.firstName} ${user.lastName}`;
    }
    return user.name || 'Anonymous';
  };

  // Filter threads by category
  const getFilteredThreads = () => {
    if (!forum?.threads) return [];

    let filtered = forum.threads;

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(thread => {
        if (selectedCategory === 'general') return !thread.isQuestion && thread.category !== 'help';
        if (selectedCategory === 'help') return thread.category === 'help';
        if (selectedCategory === 'qa') return thread.isQuestion;
        return true;
      });
    }

    // Sort threads
    switch (sortBy) {
      case 'popular':
        return filtered.sort((a, b) => (b.likes?.length || 0) - (a.likes?.length || 0));
      case 'views':
        return filtered.sort((a, b) => (b.views || 0) - (a.views || 0));
      case 'replies':
        return filtered.sort((a, b) => (b.replies?.length || 0) - (a.replies?.length || 0));
      case 'recent':
      default:
        return filtered.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));
    }
  };

  if (loading) {
    return (
      <div className="forum-loading">
        <div className="loading-spinner"></div>
        <p>Loading forum...</p>
      </div>
    );
  }

  return (
    <div className="course-forum">
      <div className="forum-header">
        <div className="forum-title">
          <FaComments className="forum-icon" />
          <h3>Course Forum</h3>
        </div>
        <button 
          className="new-thread-btn"
          onClick={() => setShowNewThreadForm(true)}
        >
          <FaPlus /> New Thread
        </button>
      </div>

      {/* Forum Filters */}
      <div className="forum-filters">
        <div className="category-filters">
          {categories.map((category) => {
            const IconComponent = category.icon;
            return (
              <button
                key={category.id}
                className={`category-btn ${selectedCategory === category.id ? 'active' : ''}`}
                onClick={() => setSelectedCategory(category.id)}
              >
                <IconComponent className="category-icon" />
                {category.name}
              </button>
            );
          })}
        </div>

        <div className="sort-options">
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="sort-select"
          >
            <option value="recent">Most Recent</option>
            <option value="popular">Most Popular</option>
            <option value="views">Most Viewed</option>
            <option value="replies">Most Replies</option>
          </select>
        </div>
      </div>

      {/* New Thread Form */}
      {showNewThreadForm && (
        <div className="new-thread-form">
          <div className="form-header">
            <h4>Create New Thread</h4>
            <button 
              className="close-form-btn"
              onClick={() => setShowNewThreadForm(false)}
            >
              <FaTimes />
            </button>
          </div>
          <form onSubmit={handleCreateThread}>
            <div className="form-group">
              <input
                type="text"
                placeholder="Thread title..."
                value={newThread.title}
                onChange={(e) => setNewThread({...newThread, title: e.target.value})}
                required
              />
            </div>
            <div className="form-group">
              <textarea
                placeholder="What would you like to discuss?"
                value={newThread.content}
                onChange={(e) => setNewThread({...newThread, content: e.target.value})}
                rows="4"
                required
              />
            </div>
            <div className="form-group">
              <select
                value={newThread.category}
                onChange={(e) => setNewThread({...newThread, category: e.target.value})}
                className="category-select"
              >
                <option value="general">General Discussion</option>
                <option value="help">Help & Support</option>
                <option value="qa">Q&A</option>
              </select>
            </div>
            <div className="form-options">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={newThread.isQuestion}
                  onChange={(e) => setNewThread({...newThread, isQuestion: e.target.checked})}
                />
                This is a question
              </label>
              <input
                type="text"
                placeholder="Tags (comma separated)"
                value={newThread.tags}
                onChange={(e) => setNewThread({...newThread, tags: e.target.value})}
                className="tags-input"
              />
            </div>
            <div className="form-actions">
              <button type="button" onClick={() => setShowNewThreadForm(false)}>
                Cancel
              </button>
              <button type="submit" className="submit-btn">
                Create Thread
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Threads List */}
      <div className="threads-list">
        {getFilteredThreads().length > 0 ? (
          getFilteredThreads().map((thread) => (
              <div key={thread._id} className="thread-item">
                <div className="thread-header">
                  <div className="thread-meta">
                    {thread.isQuestion && <FaQuestion className="question-icon" />}
                    {thread.isPinned && <FaExclamation className="pinned-icon" />}
                    {thread.isSolved && <FaCheckCircle className="solved-icon" />}
                  </div>
                  <h4 
                    className="thread-title"
                    onClick={() => {
                      setSelectedThread(selectedThread === thread._id ? null : thread._id);
                      if (selectedThread !== thread._id) {
                        incrementViews(thread._id);
                      }
                    }}
                  >
                    {thread.title}
                  </h4>
                </div>
                
                <div className="thread-info">
                  <div className="thread-author">
                    <img
                      src={thread.author?.profile?.avatar
                        ? `${VITE_IMAGE_URL}${thread.author.profile.avatar}`
                        : 'https://via.placeholder.com/32?text=User'}
                      alt={getUserDisplayName(thread.author)}
                      className="author-avatar"
                    />
                    <span className="author-name">
                      {getUserDisplayName(thread.author)}
                      {thread.author?.role && (
                        <small className="author-role">
                          ({thread.author.role === 'admin' ? 'Admin' :
                            thread.author.role === 'staff' ? 'Staff' :
                              thread.author.role === 'university' ? 'School' :
                                'Educator'})
                        </small>
                      )}
                    </span>
                  </div>
                  <div className="thread-stats">
                    <span className="thread-date">{formatDate(thread.createdAt)}</span>
                    <span className="thread-views">
                      <FaEye /> {thread.views || 0}
                    </span>
                    <span className="thread-replies">
                      {thread.replies?.length || 0} replies
                    </span>
                  </div>
                </div>

                {thread.tags && thread.tags.length > 0 && (
                  <div className="thread-tags">
                    {thread.tags.map((tag, index) => (
                      <span key={index} className="thread-tag">{tag}</span>
                    ))}
                  </div>
                )}

                {/* Thread Content and Replies */}
                {selectedThread === thread._id && (
                  <div className="thread-details">
                    <div className="thread-content">
                      <p>{thread.content}</p>
                      <div className="thread-actions">
                        <button 
                          className={`like-btn ${thread.likes?.some(like => like.user === user?.id) ? 'liked' : ''}`}
                          onClick={() => handleLikeThread(thread._id)}
                        >
                          <FaThumbsUp /> {thread.likes?.length || 0}
                        </button>
                        <button 
                          className="reply-btn"
                          onClick={() => setReplyingTo(replyingTo === thread._id ? null : thread._id)}
                        >
                          <FaReply /> Reply
                        </button>
                      </div>
                    </div>

                    {/* Reply Form */}
                    {replyingTo === thread._id && (
                      <div className="reply-form">
                        <textarea
                          placeholder="Write your reply..."
                          value={replyContent}
                          onChange={(e) => setReplyContent(e.target.value)}
                          rows="3"
                        />
                        <div className="reply-actions">
                          <button onClick={() => setReplyingTo(null)}>Cancel</button>
                          <button 
                            className="submit-reply-btn"
                            onClick={() => handleAddReply(thread._id)}
                          >
                            Post Reply
                          </button>
                        </div>
                      </div>
                    )}

                    {/* Replies */}
                    {thread.replies && thread.replies.length > 0 && (
                      <div className="replies-list">
                        {thread.replies.map((reply) => (
                          <div key={reply._id} className="reply-item">
                            <div className="reply-author">
                              <img
                                src={reply.author?.profile?.avatar
                                  ? `${VITE_IMAGE_URL}${reply.author.profile.avatar}`
                                  : 'https://via.placeholder.com/24?text=User'}
                                alt={getUserDisplayName(reply.author)}
                                className="reply-avatar"
                              />
                              <span className="reply-author-name">
                                {getUserDisplayName(reply.author)}
                                {reply.author?.role && (
                                  <small className="reply-author-role">
                                    ({reply.author.role === 'admin' ? 'Admin' :
                                      reply.author.role === 'staff' ? 'Staff' :
                                        reply.author.role === 'university' ? 'School' :
                                          'Educator'})
                                  </small>
                                )}
                              </span>
                              <span className="reply-date">{formatDate(reply.createdAt)}</span>
                            </div>
                            <div className="reply-content">
                              <p>{reply.content}</p>
                              {reply.isAnswer && (
                                <span className="answer-badge">✓ Answer</span>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))
        ) : (
          <div className="no-threads">
            <FaComments className="no-threads-icon" />
            <h4>No discussions yet</h4>
            <p>Be the first to start a discussion in this course forum!</p>
            <button 
              className="start-discussion-btn"
              onClick={() => setShowNewThreadForm(true)}
            >
              Start Discussion
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default CourseForum;
