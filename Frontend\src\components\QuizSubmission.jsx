import React, { useState, useEffect } from 'react';
import './QuizSubmission.css';
import { showWarningAlert, showErrorAlert } from '../utils/alertService';
const QuizSubmission = ({ quiz, onSubmit, existingAttempt = null }) => {
  const [answers, setAnswers] = useState({});
  const [submitted, setSubmitted] = useState(false);
  const [results, setResults] = useState(null);
  const [loading, setLoading] = useState(false);
  // Check if this quiz has existing attempts when the component mounts or quiz changes
  useEffect(() => {
    // Reset state when the component mounts or quiz changes
    setSubmitted(false);
    setResults(null);
    setAnswers({});
    // Only if there's an existing attempt for the current user, show the results
    if (existingAttempt) {
      setSubmitted(true);
      setResults(existingAttempt);
      // Reconstruct answers from the attempt data if available
      if (existingAttempt.answers && Array.isArray(existingAttempt.answers)) {
        const reconstructedAnswers = {};
        existingAttempt.answers.forEach(answer => {
          if (answer.questionId) {
            reconstructedAnswers[answer.questionId] = answer.selectedAnswer;
          }
        });
        setAnswers(reconstructedAnswers);
      }
    }
  }, [existingAttempt, quiz._id]);
  const handleAnswerSelect = (questionId, answerIndex, questionType = 'single-answer') => {
    if (questionType === 'multi-answer') {
      // Handle multiple answer selection
      const currentAnswers = answers[questionId] || [];
      const answerStr = answerIndex.toString();

      let updatedAnswers;
      if (currentAnswers.includes(answerStr)) {
        // Remove if already selected
        updatedAnswers = currentAnswers.filter(ans => ans !== answerStr);
      } else {
        // Add if not selected
        updatedAnswers = [...currentAnswers, answerStr];
      }

      setAnswers({
        ...answers,
        [questionId]: updatedAnswers
      });
    } else {
      // Handle single answer selection (legacy)
      setAnswers({
        ...answers,
        [questionId]: answerIndex.toString()
      });
    }
  };
  const handleSubmit = async () => {
    // Check if all questions are answered (handle both single and multi-answer)
    const allAnswered = quiz.questions.every(q => {
      const answer = answers[q._id];
      if (q.questionType === 'multi-answer') {
        return Array.isArray(answer) && answer.length > 0;
      } else {
        return answer !== undefined && answer !== null && answer !== '';
      }
    });

    if (!allAnswered) {
      showWarningAlert('Please answer all questions before submitting.');
      return;
    }
    try {
      setLoading(true);
      const result = await onSubmit(answers);
      setResults(result);
      setSubmitted(true);
    } catch (error) {
      console.error('Error submitting quiz:', error);
      showErrorAlert('Failed to submit quiz. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  const handleRetry = () => {
    // Only reset the local state, don't delete the attempt from the database
    setAnswers({});
    setSubmitted(false);
    setResults(null);
  };
  return (
    <div className="quiz-submission-container">
      {!submitted ? (
        <>
          <div className="quiz-header">
            <h2>{quiz.title}</h2>
            {quiz.description && <p className="quiz-description">{quiz.description}</p>}

            {/* Quiz Information Panel */}
            <div className="quiz-info-panel">
              <div className="quiz-info-item">
                <span className="info-label">Passing Score:</span>
                <span className="info-value">{quiz.passingScore || 60}%</span>
              </div>
              <div className="quiz-info-item">
                <span className="info-label">Questions:</span>
                <span className="info-value">{quiz.questions.length}</span>
              </div>
            </div>

            <div className="quiz-requirements-notice">
              <p>
                <strong>Certificate Eligibility:</strong> You must score {quiz.passingScore || 60}% or higher
                to be eligible for a course completion certificate.
              </p>
            </div>
          </div>
          <div className="quiz-questions">
            {quiz.questions.map((question, qIndex) => {
              const questionType = question.questionType || 'single-answer';

              return (
                <div key={question._id} className="quiz-question">
                  <h3>Question {qIndex + 1}: {question.question}</h3>

                  {questionType === 'multi-answer' && (
                    <div className="quiz-partial-credit-info">
                      <h4>Multiple Answer Question</h4>
                      <p>Select all correct answers. Partial credit will be awarded based on your selections.</p>
                    </div>
                  )}

                  <div className="quiz-options">
                    {question.options.map((option, oIndex) => {
                      const optionText = typeof option === 'object' ? option.text : option;
                      const isSelected = questionType === 'multi-answer'
                        ? (answers[question._id] || []).includes(oIndex.toString())
                        : answers[question._id] === oIndex.toString();

                      return (
                        <label key={oIndex} className="quiz-option">
                          <input
                            type={questionType === 'multi-answer' ? 'checkbox' : 'radio'}
                            name={`question-${question._id}`}
                            checked={isSelected}
                            onChange={() => handleAnswerSelect(question._id, oIndex, questionType)}
                          />
                          <span>{optionText}</span>
                        
                        </label>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>
          <div className="quiz-actions">
            <button
              className="quiz-submit-btn"
              onClick={handleSubmit}
              disabled={loading}
            >
              {loading ? 'Submitting...' : 'Submit Quiz'}
            </button>
          </div>
        </>
      ) : (
        <div className="quiz-results">
          <div className="quiz-result-header">
            <h2>{results.passed ? 'Congratulations!' : 'Quiz Results'}</h2>
            <div className="quiz-score">
              <div className={`score-circle ${results.passed ? 'passed' : 'failed'}`}>
                {results.percentage}%
              </div>
              <p>You {results.passed ? 'passed' : 'did not pass'} the quiz.</p>
              <p>Score: {results.earnedPoints || results.score}/{results.totalPoints}</p>
              {results.passingScore && (
                <p>Passing Score: {results.passingScore}%</p>
              )}

              {/* Certificate Eligibility Status */}
              <div className={`certificate-eligibility ${results.passed ? 'eligible' : 'not-eligible'}`}>
                {results.passed ? (
                  <div className="eligibility-message success">
                    <span className="eligibility-icon">🎉</span>
                    <div>
                      <strong>Certificate Eligible!</strong>
                      <p>You've met the passing score requirement and are eligible for a course completion certificate.</p>
                    </div>
                  </div>
                ) : (
                  <div className="eligibility-message failed">
                    <span className="eligibility-icon">📋</span>
                    <div>
                      <strong>Certificate Not Available</strong>
                      <p>You need to score {results.passingScore || 60}% or higher to be eligible for a certificate. You can retake the quiz to improve your score.</p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {results.detailedScore && (
              <div className="quiz-score-breakdown">
                <h4>Score Breakdown</h4>
                {results.detailedScore.partialCredits.map((credit, index) => (
                  <div key={index} className="question-score-item">
                    <span className="question-score-text">
                      Question {index + 1}
                    </span>
                    <span className="question-score-points">
                      {credit.earnedPoints.toFixed(1)}/{credit.maxPoints}
                      {credit.earnedPoints === credit.maxPoints ? (
                        <span className="full-credit-badge">Full Credit</span>
                      ) : credit.earnedPoints > 0 ? (
                        <span className="partial-credit-badge">Partial Credit</span>
                      ) : (
                        <span className="no-credit-badge">No Credit</span>
                      )}
                    </span>
                  </div>
                ))}
              </div>
            )}
          </div>
          <div className="quiz-answers-review">
            <h3>Review Your Answers</h3>
            {quiz.questions.map((question, qIndex) => {
              // Find the corresponding answer from results if available
              const answerFromResults = results.answers &&
                results.answers.find(a => a.questionIndex === qIndex);

              // Handle both single and multi-answer questions
              const userAnswers = answerFromResults ?
                (answerFromResults.selectedAnswers || [answerFromResults.selectedAnswer]) :
                (Array.isArray(answers[question._id]) ? answers[question._id] : [answers[question._id]]);

              const partialCredit = answerFromResults ? answerFromResults.partialCredit : 0;
              const questionType = question.questionType || 'single-answer';

              // Determine if question was answered correctly (full or partial credit)
              const hasCredit = partialCredit > 0;
              const hasFullCredit = partialCredit === 100;

              return (
                <div key={question._id} className={`review-question ${hasFullCredit ? 'correct' : hasCredit ? 'partial' : 'incorrect'}`}>
                  <h4>
                    Question {qIndex + 1}: {question.question}
                    {partialCredit > 0 && partialCredit < 100 && (
                      <span className="partial-credit-badge">{partialCredit}% Credit</span>
                    )}
                    {partialCredit === 100 && (
                      <span className="full-credit-badge">Full Credit</span>
                    )}
                    {partialCredit === 0 && (
                      <span className="no-credit-badge">No Credit</span>
                    )}
                  </h4>
                  <div className="review-options">
                    {question.options.map((option, oIndex) => {
                      const optionText = typeof option === 'object' ? option.text : option;
                      const optionWeight = typeof option === 'object' ? option.weight : 0;
                      const isCorrectOption = typeof option === 'object' ? option.isCorrect : false;

                      // Check if user selected this option
                      const isSelected = userAnswers.includes(oIndex.toString());

                      // Legacy fallback for correct answer
                      const isLegacyCorrect = question.correctAnswer === oIndex.toString();
                      const isCorrect = isCorrectOption || isLegacyCorrect;

                      return (
                        <div
                          key={oIndex}
                          className={`review-option ${isSelected ? 'selected' : ''} ${isCorrect ? 'correct-answer' : ''}`}
                        >
                          <span className="option-text">{optionText}</span>
                          {optionWeight > 0 && (
                            <span className="option-weight">({optionWeight}%)</span>
                          )}
                          {isSelected && !isCorrect && (
                            <span className="wrong-indicator">✗</span>
                          )}
                          {isCorrect && (
                            <span className="correct-indicator">✓</span>
                          )}
                        </div>
                      );
                    })}
                  </div>

                  {questionType === 'multi-answer' && (
                    <div className="multi-answer-summary">
                      <p>Selected {userAnswers.filter(a => a).length} of {question.options.filter(opt =>
                        typeof opt === 'object' ? opt.isCorrect : false
                      ).length} correct answers</p>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
          <div className="quiz-actions">
            <button className="quiz-retry-btn" onClick={handleRetry}>
              Try Again
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
export default QuizSubmission;