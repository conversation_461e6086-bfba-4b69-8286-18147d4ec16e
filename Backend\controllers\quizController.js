const Quiz = require('../models/Quiz');
const Course = require('../models/Course');
const AppError = require('../utils/appError');
const catchAsync = require('../utils/catchAsync');

// Create a new quiz
exports.createQuiz = catchAsync(async (req, res, next) => {
  const { courseId } = req.params;
  const course = await Course.findById(courseId);

  if (!course) {
    return next(new AppError('Course not found', 404));
  }

  if (course.creator.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    return next(new AppError('Not authorized to create quiz for this course', 403));
  }

  const quiz = await Quiz.create({
    ...req.body,
    course: courseId
  });

  course.quizzes.push(quiz._id);
  await course.save();

  res.status(201).json({
    status: 'success',
    data: quiz
  });
});

// Get a quiz
exports.getQuiz = catchAsync(async (req, res, next) => {
  const quiz = await Quiz.findById(req.params.quizId)
    .populate('course', 'title');

  if (!quiz) {
    return next(new AppError('Quiz not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: quiz
  });
});

// Update a quiz
exports.updateQuiz = catchAsync(async (req, res, next) => {
  const quiz = await Quiz.findById(req.params.quizId);

  if (!quiz) {
    return next(new AppError('Quiz not found', 404));
  }

  const course = await Course.findById(quiz.course);
  if (course.creator.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    return next(new AppError('Not authorized to update this quiz', 403));
  }

  const updatedQuiz = await Quiz.findByIdAndUpdate(
    req.params.quizId,
    req.body,
    { new: true, runValidators: true }
  );

  res.status(200).json({
    status: 'success',
    data: updatedQuiz
  });
});

// Delete a quiz
exports.deleteQuiz = catchAsync(async (req, res, next) => {
  const quiz = await Quiz.findById(req.params.quizId);

  if (!quiz) {
    return next(new AppError('Quiz not found', 404));
  }

  const course = await Course.findById(quiz.course);
  if (course.creator.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    return next(new AppError('Not authorized to delete this quiz', 403));
  }

  await Quiz.findByIdAndDelete(req.params.quizId);

  // Remove quiz reference from course
  course.quizzes = course.quizzes.filter(q => q.toString() !== quiz._id.toString());
  await course.save();

  res.status(204).json({
    status: 'success',
    data: null
  });
});

// Submit quiz attempt
exports.submitQuiz = catchAsync(async (req, res, next) => {
  const quiz = await Quiz.findById(req.params.quizId);

  if (!quiz) {
    return next(new AppError('Quiz not found', 404));
  }

  const { answers } = req.body;
  let totalEarnedPoints = 0;
  let totalPossiblePoints = 0;
  const detailedScores = [];

  // Handle different answer formats (array or object with question IDs as keys)
  let evaluatedAnswers = [];

  if (Array.isArray(answers)) {
    // Handle legacy array format
    evaluatedAnswers = answers.map((answer, index) => {
      const question = quiz.questions[index];
      const questionPoints = question.points || 1;
      totalPossiblePoints += questionPoints;

      let earnedPoints = 0;
      let isCorrect = false;
      let partialCredit = 0;

      // Check if question uses new weighted system
      if (question.options && question.options.length > 0 && question.options.some(opt => opt.weight > 0)) {
        // New weighted system
        const selectedOptionIndex = parseInt(answer);
        if (selectedOptionIndex >= 0 && selectedOptionIndex < question.options.length) {
          const selectedOption = question.options[selectedOptionIndex];
          partialCredit = selectedOption.weight || 0;
          earnedPoints = (partialCredit / 100) * questionPoints;
          isCorrect = selectedOption.isCorrect;
        }
      } else {
        // Legacy system
        isCorrect = answer === question.correctAnswer;
        if (isCorrect) {
          earnedPoints = questionPoints;
          partialCredit = 100;
        }
      }

      totalEarnedPoints += earnedPoints;

      detailedScores.push({
        questionIndex: index,
        maxPoints: questionPoints,
        earnedPoints: earnedPoints,
        selectedOptions: [parseInt(answer)]
      });

      return {
        questionIndex: index,
        questionId: question._id,
        selectedAnswer: answer, // Keep as string for legacy format
        selectedAnswers: [answer],
        correctAnswer: question.correctAnswer,
        isCorrect,
        partialCredit
      };
    });
  } else {
    // Handle object format with question IDs as keys
    evaluatedAnswers = quiz.questions.map((question, index) => {
      const questionId = question._id.toString();
      const selectedAnswers = Array.isArray(answers[questionId]) ? answers[questionId] : [answers[questionId]];
      const questionPoints = question.points || 1;
      totalPossiblePoints += questionPoints;

      let earnedPoints = 0;
      let isCorrect = false;
      let partialCredit = 0;
      const selectedOptionIndices = [];

      // Check if question uses new weighted system
      if (question.options && question.options.length > 0 && question.options.some(opt => opt.weight > 0)) {
        // New weighted system - calculate partial credit
        for (const answer of selectedAnswers) {
          if (answer !== undefined && answer !== null && answer !== '') {
            const selectedOptionIndex = parseInt(answer);
            if (selectedOptionIndex >= 0 && selectedOptionIndex < question.options.length) {
              selectedOptionIndices.push(selectedOptionIndex);
              const selectedOption = question.options[selectedOptionIndex];
              partialCredit += selectedOption.weight || 0;
              if (selectedOption.isCorrect) {
                isCorrect = true;
              }
            }
          }
        }

        // Cap partial credit at 100%
        partialCredit = Math.min(partialCredit, 100);
        earnedPoints = (partialCredit / 100) * questionPoints;
      } else {
        // Legacy system
        const primaryAnswer = selectedAnswers[0];
        isCorrect = primaryAnswer === question.correctAnswer;
        if (isCorrect) {
          earnedPoints = questionPoints;
          partialCredit = 100;
        }
        if (primaryAnswer !== undefined) {
          selectedOptionIndices.push(parseInt(primaryAnswer));
        }
      }

      totalEarnedPoints += earnedPoints;

      detailedScores.push({
        questionIndex: index,
        maxPoints: questionPoints,
        earnedPoints: earnedPoints,
        selectedOptions: selectedOptionIndices
      });

      return {
        questionIndex: index,
        questionId: questionId,
        selectedAnswer: selectedAnswers.length === 1 ? selectedAnswers[0] : selectedAnswers, // Handle both single and multi-answer
        selectedAnswers: selectedAnswers,
        correctAnswer: question.correctAnswer, // Legacy compatibility
        isCorrect,
        partialCredit
      };
    });
  }

  const finalScore = totalPossiblePoints > 0 ? Math.round((totalEarnedPoints / totalPossiblePoints) * 100) : 0;

  // Determine passing score (use module-specific or quiz default)
  const passingScore = quiz.moduleRequirement?.customPassingScore || quiz.passingScore || 60;
  const passed = finalScore >= passingScore;

  // Save the attempt with detailed scoring
  const attempt = {
    user: req.user._id,
    score: finalScore,
    detailedScore: {
      totalPoints: totalPossiblePoints,
      earnedPoints: totalEarnedPoints,
      partialCredits: detailedScores
    },
    answers: evaluatedAnswers,
    passed,
    completedAt: new Date()
  };

  quiz.attempts.push(attempt);
  await quiz.save();

  // Update module progress if this quiz is associated with a module
  const Module = require('../models/Module');
  const module = await Module.findOne({ quiz: quiz._id });
  if (module) {
    await updateModuleQuizProgress(req.user._id, module.course, module._id, {
      hasAttempted: true,
      hasPassed: passed,
      bestScore: finalScore,
      lastAttemptScore: finalScore,
      lastAttemptAt: new Date(),
      passedAt: passed ? new Date() : undefined
    });
  }

  // Return detailed results including correct answers
  res.status(200).json({
    status: 'success',
    data: {
      score: finalScore,
      totalPoints: totalPossiblePoints,
      earnedPoints: totalEarnedPoints,
      percentage: finalScore,
      passed,
      passingScore,
      answers: evaluatedAnswers,
      detailedScore: {
        totalPoints: totalPossiblePoints,
        earnedPoints: totalEarnedPoints,
        partialCredits: detailedScores
      },
      questions: quiz.questions.map(q => ({
        id: q._id,
        question: q.question,
        options: q.options,
        correctAnswer: q.correctAnswer,
        points: q.points || 1
      }))
    }
  });
});

// Get quiz results
exports.getQuizResults = catchAsync(async (req, res, next) => {
  const quiz = await Quiz.findById(req.params.quizId)
    .populate('attempts.user', 'name email');

  if (!quiz) {
    return next(new AppError('Quiz not found', 404));
  }

  const userAttempt = quiz.attempts.find(
    attempt => attempt.user._id.toString() === req.user._id.toString()
  );

  if (!userAttempt) {
    return next(new AppError('No attempt found for this quiz', 404));
  }

  res.status(200).json({
    status: 'success',
    data: userAttempt
  });
});

// Helper function to update module quiz progress
const updateModuleQuizProgress = async (userId, courseId, moduleId, quizProgress) => {
  const UserModuleProgress = require('../models/UserModuleProgress');

  let progress = await UserModuleProgress.findOne({ user: userId, course: courseId });

  if (!progress) {
    // Create new progress record if it doesn't exist
    const Course = require('../models/Course');
    const course = await Course.findById(courseId).populate('modules');

    const moduleProgress = course.modules.map(module => ({
      module: module._id,
      isCompleted: false,
      completedContent: [],
      quizProgress: module._id.toString() === moduleId.toString() ? quizProgress : {
        hasAttempted: false,
        hasPassed: false,
        bestScore: 0,
        lastAttemptScore: 0,
        attemptCount: 0
      },
      contentCompleted: false,
      quizCompleted: false,
      meetsCompletionCriteria: false,
      lastAccessedAt: new Date()
    }));

    progress = new UserModuleProgress({
      user: userId,
      course: courseId,
      moduleProgress,
      lastAccessedModule: moduleId
    });
  } else {
    // Update existing progress
    const moduleProgressIndex = progress.moduleProgress.findIndex(
      mp => mp.module.toString() === moduleId.toString()
    );

    if (moduleProgressIndex >= 0) {
      const currentQuizProgress = progress.moduleProgress[moduleProgressIndex].quizProgress;

      // Update quiz progress
      progress.moduleProgress[moduleProgressIndex].quizProgress = {
        ...currentQuizProgress,
        ...quizProgress,
        attemptCount: (currentQuizProgress.attemptCount || 0) + 1,
        bestScore: Math.max(currentQuizProgress.bestScore || 0, quizProgress.lastAttemptScore || 0)
      };

      // Update quiz completion status
      progress.moduleProgress[moduleProgressIndex].quizCompleted = quizProgress.hasPassed;

      // Check if module completion criteria are met
      const moduleProgress = progress.moduleProgress[moduleProgressIndex];
      const Module = require('../models/Module');
      const module = await Module.findById(moduleId);

      if (module) {
        const contentComplete = moduleProgress.contentCompleted;
        const quizComplete = !module.completionCriteria?.requireQuizPass || moduleProgress.quizCompleted;

        moduleProgress.meetsCompletionCriteria = contentComplete && quizComplete;
        moduleProgress.isCompleted = moduleProgress.meetsCompletionCriteria;
      }
    }
  }

  await progress.save();
};