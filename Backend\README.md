# IIM-<PERSON><PERSON> Backend

The backend server for the IIM Learning Management System, built with Node.js, Express, and MongoDB.

## Features

- **RESTful API**: Well-structured API endpoints for all platform functionality
- **Authentication**: JWT-based authentication with refresh tokens and secure logout
- **Authorization**: Role-based access control (RBAC) for Educators, University Admins, Super Admins, and My Team
- **Security**: Comprehensive security measures including:
  - Password hashing with bcrypt
  - CORS protection
  - HTTP security headers with Helmet
  - Input validation with Joi
  - MongoDB query sanitization
  - XSS protection
  - Rate limiting
- **Email Integration**: Password reset, notifications, and newsletter functionality
- **File Storage**: Integration with Cloudinary for content uploads
- **Certificate System**: Automated certificate generation and verification
- **Forum System**: Discussion forums with threaded conversations
- **Newsletter Management**: Smart subscription system with status tracking
- **Database Seeding**: Initial data population for testing and development

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
  - **Body**:
    ```json
    {
      "email": "<EMAIL>",
      "password": "your_password"
    }
    ```
- `POST /api/auth/refresh-token` - Refresh access token
  - **Body**:
    ```json
    {
      "token": "your_refresh_token"
    }
    ```
- `POST /api/auth/logout` - User logout
- `POST /api/auth/forgot-password` - Request password reset
  - **Body**:
    ```json
    {
      "email": "<EMAIL>"
    }
    ```
- `POST /api/auth/reset-password` - Reset password with token
  - **Body**:
    ```json
    {
      "token": "your_reset_token",
      "password": "new_password"
    }
    ```

### Educator
- `GET /api/educator/courses` - Get available courses
- `POST /api/educator/courses/:id/enroll` - Enroll in a course
  - **Body**:
    ```json
    {
      "userId": "your_user_id"
    }
    ```
- `GET /api/educator/my-courses` - Get enrolled courses
- `GET /api/educator/course/:id` - Get course details
- `GET /api/educator/content` - Get content list
- `POST /api/educator/content` - Create new content
  - **Body**:
    ```json
    {
      "title": "Content Title",
      "description": "Content Description",
      "file": "file_url"
    }
    ```
- `PUT /api/educator/content/:id` - Update content
  - **Body**:
    ```json
    {
      "title": "Updated Title",
      "description": "Updated Description"
    }
    ```
- `DELETE /api/educator/content/:id` - Delete content
- `GET /api/educator/profile` - Get profile
- `PUT /api/educator/profile` - Update profile
  - **Body**:
    ```json
    {
      "name": "Your Name",
      "email": "<EMAIL>"
    }
    ```

### University Admin
- `GET /api/university/educators` - Get educators list
- `POST /api/university/educator` - Create educator account
  - **Body**:
    ```json
    {
      "name": "Educator Name",
      "email": "<EMAIL>",
      "password": "educator_password"
    }
    ```
- `PUT /api/university/educator/:id` - Update educator
  - **Body**:
    ```json
    {
      "name": "Updated Educator Name",
      "email": "<EMAIL>"
    }
    ```
- `GET /api/university/profile` - Get university profile
- `PUT /api/university/profile` - Update university profile
  - **Body**:
    ```json
    {
      "name": "University Name",
      "address": "University Address"
    }
    ```

### Super Admin
- `GET /api/admin/universities` - Get universities list
- `POST /api/admin/university` - Create university
  - **Body**:
    ```json
    {
      "name": "New University",
      "location": "University Location"
    }
    ```
- `PUT /api/admin/university/:id` - Update university
  - **Body**:
    ```json
    {
      "name": "Updated University Name",
      "location": "Updated Location"
    }
    ```
- `GET /api/admin/content` - Get all content
- `PUT /api/admin/content/approve/:id` - Approve content
- `PUT /api/admin/content/reject/:id` - Reject content
- `GET /api/admin/courses` - Get all courses

### CMS
- `GET /api/cms/pages` - Get published pages
- `GET /api/cms/page/:slug` - Get page by slug

### Quiz
- `POST /api/quiz/:courseId` - Create quiz
  - **Body**:
    ```json
    {
      "title": "Quiz Title",
      "questions": [
        {
          "question": "What is your question?",
          "options": ["Option 1", "Option 2"],
          "answer": "Option 1"
        }
      ]
    }
    ```
- `GET /api/quiz/:quizId` - Get quiz
- `PUT /api/quiz/:quizId` - Update quiz
  - **Body**:
    ```json
    {
      "title": "Updated Quiz Title"
    }
    ```
- `DELETE /api/quiz/:quizId` - Delete quiz
- `POST /api/quiz/:quizId/submit` - Submit quiz attempt
  - **Body**:
    ```json
    {
      "answers": [
        {
          "questionId": "question_id",
          "selectedOption": "Option 1"
        }
      ]
    }
    ```
- `GET /api/quiz/:quizId/results` - Get quiz results

### Newsletter & Subscription
- `POST /api/subscriber/subscribe` - Subscribe to newsletter
  - **Body**:
    ```json
    {
      "email": "<EMAIL>",
      "name": "User Name"
    }
    ```
- `GET /api/subscriber/check-status?email=<EMAIL>` - Check subscription status
- `GET /api/subscriber/unsubscribe/:token` - Unsubscribe from newsletter
- `GET /api/subscriber/list` - Get subscribers list (Admin only)
- `GET /api/subscriber/stats` - Get subscription statistics (Admin only)

### Newsletter Management
- `GET /api/newsletter` - Get all newsletters (Admin only)
- `POST /api/newsletter` - Create newsletter (Admin only)
  - **Body**:
    ```json
    {
      "title": "Newsletter Title",
      "content": "Newsletter content",
      "type": "general"
    }
    ```
- `PUT /api/newsletter/:id` - Update newsletter (Admin only)
- `DELETE /api/newsletter/:id` - Delete newsletter (Admin only)
- `POST /api/newsletter/:id/send` - Send newsletter to subscribers (Admin only)

### Forum
- `GET /api/forum/course/:courseId` - Get course forum threads
- `POST /api/forum/course/:courseId` - Create new forum thread
  - **Body**:
    ```json
    {
      "title": "Thread Title",
      "content": "Thread content"
    }
    ```
- `GET /api/forum/thread/:threadId` - Get thread with replies
- `POST /api/forum/thread/:threadId/reply` - Reply to thread
  - **Body**:
    ```json
    {
      "content": "Reply content"
    }
    ```

### Certificate
- `POST /api/certificate/generate/:courseId` - Generate certificate for completed course
- `GET /api/certificate/verify/:certificateId` - Verify certificate (Public)
- `GET /api/certificate/view/:certificateId` - View certificate (Public)
- `GET /api/certificate/user/:userId` - Get user's certificates

## Database Models

- **User**: Educators, University Admins, Super Admins, and My Team members
- **Course**: Educational courses with modules and progress tracking
- **Content**: Educational materials and resources
- **Quiz**: Assessment tools with automatic grading
- **Certificate**: Course completion certificates with verification
- **CmsPage**: Content management system pages
- **Subscriber**: Newsletter subscription management
- **Newsletter**: Newsletter content and distribution
- **Forum**: Discussion forums and threads
- **Comment**: Forum replies and discussions
- **Role**: Dynamic role management with permissions

## Getting Started

### Prerequisites
- Node.js (v14 or higher)
- MongoDB
- Cloudinary account (for file uploads)
- SMTP server access (for emails)

### Installation

1. Clone the repository
