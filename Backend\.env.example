# This is an example environment configuration file.
# Please replace the values with your own credentials.

# Database Configuration
MONGO_URI=your_mongo_uri_here
MONGO_DB_NAME="your_database_name_here"

# Server Configuration
PORT=5000
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d
JWT_COOKIE_EXPIRES_IN=7

# File Storage (Cloudinary)
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name_here
CLOUDINARY_API_KEY=your_cloudinary_api_key_here
CLOUDINARY_API_SECRET=your_cloudinary_api_secret_here

# Email Configuration
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_USER=your_email_here
EMAIL_PASS=your_email_password_here
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=IIM Education

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=your_twilio_account_sid_here
TWILIO_AUTH_TOKEN=your_twilio_auth_token_here
TWILIO_PHONE_NUMBER=your_twilio_phone_number_here
DISABLE_SMS_DELIVERY=true # Set to true in development to skip actual SMS delivery

# Environment Settings
NODE_ENV=development
API_VERSION=v1
FRONTEND_URL=http://localhost:5173

# Security Settings
CORS_ORIGIN=http://localhost:5173
RATE_LIMIT_WINDOW_MS=900000 # 15 minutes
RATE_LIMIT_MAX=100 # 100 requests per window

# Certificate Generation
CERTIFICATE_SECRET=your_certificate_secret_here
CERTIFICATE_BASE_URL=http://localhost:5000/api/certificate/verify

# Production Debugging (optional)
DEBUG_PROXY=false # Set to true in production to debug proxy headers

# HTTPS Configuration (optional)
HTTPS_PORT=5443 # Port for HTTPS server if SSL certificates are available