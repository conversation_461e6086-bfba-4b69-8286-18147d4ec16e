import React, { useState, useEffect } from "react";
import {
  FaPlus,
  FaTrash,
  FaEdit,
  FaGripVertical,
  FaVideo,
  FaFileAlt,
  FaFileUpload,
  FaQuestionCircle,
  FaFileAlt as FaFileText,
  FaYoutube,
  FaChevronDown,
  FaChevronUp,
} from "react-icons/fa";
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd";
import SummernoteEditor from "../Editor/SummernoteEditor";
import { showWarningAlert, showConfirm } from "../../utils/alertService";

const CurriculumStep = ({ courseData, updateCourseData }) => {
  // Local state for curriculum management
  const [modules, setModules] = useState(courseData.modules || []);
  const [content, setContent] = useState(courseData.content || []);
  const [quizzes, setQuizzes] = useState(courseData.quizzes || []);

  // State for module editing
  const [editingModuleId, setEditingModuleId] = useState(null);
  const [moduleFormData, setModuleFormData] = useState({
    title: "",
    description: "",
    isCompulsory: true,
  });

  // State for content editing
  const [editingContentId, setEditingContentId] = useState(null);
  const [contentFormData, setContentFormData] = useState({
    title: "",
    description: "",
    type: "document",
    file: null,
    textContent: "",
  });

  // State for quiz editing
  const [editingQuizId, setEditingQuizId] = useState(null);
  const [quizFormData, setQuizFormData] = useState({
    title: "",
    description: "",
    questions: [],
    passingScore: 60,
  });
  const [currentQuestion, setCurrentQuestion] = useState({
    question: "",
    options: [
      { text: "", weight: 0, isCorrect: false },
      { text: "", weight: 0, isCorrect: false },
      { text: "", weight: 0, isCorrect: false },
      { text: "", weight: 0, isCorrect: false }
    ],
    questionType: "single-answer",
    correctAnswer: 0, // Legacy compatibility
  });
  const [editingQuestionIndex, setEditingQuestionIndex] = useState(null);

  // State for expanded modules (accordion behavior)
  const [expandedModules, setExpandedModules] = useState({});

  // Track if we've updated course data to prevent infinite loop
  const [hasUpdatedCourseData, setHasUpdatedCourseData] = useState(false);

  // Initialize expanded state for all modules
  useEffect(() => {
    if (modules.length > 0 && Object.keys(expandedModules).length === 0) {
      const initialExpandedState = {};
      // Only set the first module to be expanded initially
      modules.forEach((module, index) => {
        initialExpandedState[module._id] = index === 0; // Only first module is expanded
      });
      setExpandedModules(initialExpandedState);
    }
  }, [modules]);

  // Update parent component state when local states change
  useEffect(() => {
    if (hasUpdatedCourseData) {
      // Create a deep copy of modules with all nested data properly structured
      const processedModules = modules.map((module) => {
        const moduleData = {
          _id: module._id,
          title: module.title,
          description: module.description,
          order: module.order || 0,
          isCompulsory: module.isCompulsory,
          // Process content array to ensure it contains full content objects, not just IDs
          content: module.content
            ? module.content.map((contentId) => {
              if (typeof contentId === "object") {
                return contentId;
              } else {
                // Find the content item in the content array
                const contentItem = content.find(
                  (item) => item._id === contentId
                );
                return contentItem || contentId; // Return the content item if found, otherwise return the ID
              }
            })
            : [],
        };

        // Process quiz to ensure it contains the full quiz object, not just the ID
        if (module.quiz) {
          if (typeof module.quiz === "object") {
            moduleData.quiz = module.quiz;
          } else {
            const quizItem = quizzes.find((q) => q._id === module.quiz);
            if (quizItem) moduleData.quiz = quizItem;
          }
        }

        return moduleData;
      });

      // Process quizzes to ensure they have proper structure for backend
      const processedQuizzes = quizzes.map(quiz => {
        // Ensure questions have correctAnswer as string for backend compatibility
        const processedQuestions = quiz.questions.map(q => ({
          ...q,
          correctAnswer: q.correctAnswer.toString()
        }));

        // Make sure isNew flag is preserved for new quizzes
        return {
          ...quiz,
          questions: processedQuestions,
          isNew: quiz.isNew === true
        };
      });

      updateCourseData({
        modules: processedModules,
        content,
        quizzes: processedQuizzes,
      });
    } else {
      setHasUpdatedCourseData(true);
    }
  }, [modules, content, quizzes, hasUpdatedCourseData]);

  // Toggle module expanded state
  const toggleModuleExpanded = (moduleId) => {
    // Close all modules first, then open only the clicked one if it was closed
    const isCurrentlyExpanded = expandedModules[moduleId];

    // Create a new object with all modules closed
    const newExpandedState = {};
    modules.forEach((module) => {
      newExpandedState[module._id] = false;
    });

    // If the clicked module was closed, open it (otherwise all stay closed)
    if (!isCurrentlyExpanded) {
      newExpandedState[moduleId] = true;
    }

    setExpandedModules(newExpandedState);
  };

  // ===== MODULE MANAGEMENT =====

  // Add new module
  const addModule = () => {
    const newModule = {
      _id: `temp_${Date.now()}`,
      title: "New Module",
      description: "",
      content: [],
      quiz: null,
      order: modules.length,
      isCompulsory: true, // Default to true
    };

    setModules([...modules, newModule]);
    setEditingModuleId(newModule._id);
    setModuleFormData({
      title: newModule.title,
      description: newModule.description,
      isCompulsory: newModule.isCompulsory,
    });

    // Close all other modules and only expand the new one
    const newExpandedState = {};
    modules.forEach((module) => {
      newExpandedState[module._id] = false;
    });
    newExpandedState[newModule._id] = true;
    setExpandedModules(newExpandedState);
  };

  // Edit module
  const startEditModule = (module) => {
    setEditingModuleId(module._id);
    setModuleFormData({
      title: module.title,
      description: module.description,
      isCompulsory:
        module.isCompulsory !== undefined ? module.isCompulsory : true,
    });
  };

  // Save module
  const saveModule = () => {
    const updatedModules = modules.map((module) =>
      module._id === editingModuleId
        ? {
          ...module,
          title: moduleFormData.title,
          description: moduleFormData.description,
          isCompulsory: moduleFormData.isCompulsory,
        }
        : module
    );

    setModules(updatedModules);
    setEditingModuleId(null);
    setModuleFormData({ title: "", description: "", isCompulsory: true });
  };

  // Delete module
  const deleteModule = async (moduleId) => {
    const confirmed = await showConfirm("Are you sure you want to delete this module?");
    if (confirmed) {
      setModules(modules.filter((module) => module._id !== moduleId));
    }
  };

  // Reorder modules
  const onDragEnd = (result) => {
    if (!result.destination) return;

    const items = [...modules];
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Update order property
    const updatedItems = items.map((item, index) => ({
      ...item,
      order: index,
    }));

    setModules(updatedItems);
  };

  // ===== CONTENT MANAGEMENT =====

  // Add content to module
  const addContentToModule = (moduleId) => {
    const newContent = {
      _id: `temp_content_${Date.now()}`,
      title: "New Content",
      description: "",
      type: "document",
      textContent: "",
      fileUrl: "",
      module: moduleId,
    };

    const updatedModules = modules.map((module) =>
      module._id === moduleId
        ? { ...module, content: [...module.content, newContent._id] }
        : module
    );

    setModules(updatedModules);
    setContent([...content, newContent]);
    setEditingContentId(newContent._id);
    setContentFormData({
      title: newContent.title,
      description: newContent.description,
      type: newContent.type,
      file: null,
      textContent: "",
    });
  };

  // Edit content
  const startEditContent = (contentItem) => {
    setEditingContentId(contentItem._id);
    setContentFormData({
      title: contentItem.title,
      description: contentItem.description,
      type: contentItem.type || "document",
      file: null,
      textContent: contentItem.textContent || "",
      existingFileUrl: contentItem.fileUrl || "", // Store the existing file URL
      existingFileName: contentItem.fileUrl ? contentItem.fileUrl.split('/').pop() : "", // Extract filename from URL
    });

  };

  // Save content
  const saveContent = () => {
    const existingContent = content.find(
      (item) => item._id === editingContentId
    );
    const moduleOfEditingContent = existingContent?.module;

    const newContentItem = {
      ...existingContent,
      title: contentFormData.title,
      description: contentFormData.description,
      type: contentFormData.type,
      file: contentFormData.file,
      textContent:
        contentFormData.type === "youtube"
          ? contentFormData.textContent
          : contentFormData.type === "text"
            ? contentFormData.textContent
            : "",
      fileUrl:
        contentFormData.type === "youtube"
          ? contentFormData.textContent
          : contentFormData.file
            ? URL.createObjectURL(contentFormData.file)
            : contentFormData.existingFileUrl || existingContent?.fileUrl || "",
      mimeType:
        contentFormData.type === "youtube"
          ? "video/youtube"
          : contentFormData.file?.type ||
          existingContent?.mimeType ||
          "application/octet-stream",
      module: moduleOfEditingContent,
      // Preserve existing file information for backend processing
      existingFileUrl: contentFormData.existingFileUrl || existingContent?.fileUrl || "",
      existingFileName: contentFormData.existingFileName || "",
    };

    // Update content in the content array
    const updatedContent = content.map((item) =>
      item._id === editingContentId ? newContentItem : item
    );
    setContent(updatedContent);

    // Also update the content reference in the module
    if (moduleOfEditingContent) {
      // Find the module that contains this content
      const moduleToUpdate = modules.find(
        (m) => m._id === moduleOfEditingContent
      );

      if (moduleToUpdate) {
        const updatedModules = modules.map((module) => {
          if (module._id === moduleOfEditingContent) {
            // Create a new content array for this module
            const updatedModuleContent = module.content.map((contentItem) => {
              // If the content item is an object with an _id property
              if (typeof contentItem === "object" && contentItem._id === editingContentId) {
                return newContentItem;
              }
              // If the content item is a string ID
              else if (contentItem === editingContentId) {
                // We need to keep it as a string ID, but make sure the content array is updated
                // The actual content object is already updated in the content state
                return contentItem;
              }
              return contentItem;
            });

            return { ...module, content: updatedModuleContent };
          }
          return module;
        });

        setModules(updatedModules);
      }
    }

    // Make sure the course data is updated
    setHasUpdatedCourseData(true);

    // Reset form state
    setEditingContentId(null);
    setContentFormData({
      title: "",
      description: "",
      type: "document",
      file: null,
      textContent: "",
    });
  };

  // Delete content
  const deleteContent = async (contentId, moduleId = null) => {
    const confirmed = await showConfirm("Are you sure you want to delete this content?");
    if (confirmed) {
      // Remove content from array
      setContent(content.filter((item) => item._id !== contentId));

      // If the content is part of a module, update the module's content array
      if (moduleId) {
        const updatedModules = modules.map((module) => {
          if (module._id === moduleId) {
            // Handle both string IDs and object references in the content array
            const updatedContent = module.content.filter((item) => {
              if (typeof item === 'object') {
                return item._id !== contentId;
              } else {
                return item !== contentId;
              }
            });

            return {
              ...module,
              content: updatedContent,
            };
          }
          return module;
        });

        setModules(updatedModules);
      }

      // Make sure the course data is updated
      setHasUpdatedCourseData(true);
    }
  };

  // ===== QUIZ MANAGEMENT =====

  // Add quiz to module
  const addQuizToModule = (moduleId) => {
    const newQuizId = `temp_quiz_${Date.now()}`;
    const newQuiz = {
      _id: newQuizId,
      title: "Module Quiz",
      description: "Test your knowledge of this module",
      questions: [],
      passingScore: 60, // Default passing score for new quizzes
      module: moduleId,
      isNew: true, // Flag to indicate this is a new quiz being added during edit
    };

    const updatedModules = modules.map((module) =>
      module._id === moduleId ? { ...module, quiz: newQuizId } : module
    );

    setModules(updatedModules);
    setQuizzes([...quizzes, newQuiz]);
    setEditingQuizId(newQuizId);
    setQuizFormData({
      title: newQuiz.title,
      description: newQuiz.description,
      questions: [],
      passingScore: 60,
    });
  };

  // Add or update question in quiz
  const addQuestionToQuiz = () => {
    // Validate the current question
    if (
      !currentQuestion.question ||
      currentQuestion.options.some((opt) => !opt.text)
    ) {
      showWarningAlert("Please fill in all fields for the question");
      return;
    }

    // Validate weights for correct answers
    const correctOptions = currentQuestion.options.filter(opt => opt.isCorrect);
    if (correctOptions.length === 0) {
      showWarningAlert("Please mark at least one option as correct");
      return;
    }

    const totalWeight = correctOptions.reduce((sum, opt) => sum + (opt.weight || 0), 0);
    if (totalWeight !== 100) {
      showWarningAlert(`Total weight of correct answers must equal 100%. Current total: ${totalWeight}%`);
      return;
    }

    // Create a copy of the current question with both new and legacy formats
    const processedQuestion = {
      ...currentQuestion,
      correctAnswer: currentQuestion.correctAnswer.toString(), // Legacy compatibility
      options: currentQuestion.options.map(opt => ({
        text: opt.text,
        weight: opt.weight || 0,
        isCorrect: opt.isCorrect || false
      }))
    };

    let updatedQuestions;

    if (editingQuestionIndex !== null) {
      // Update existing question
      updatedQuestions = [...quizFormData.questions];
      updatedQuestions[editingQuestionIndex] = processedQuestion;
    } else {
      // Add new question
      updatedQuestions = [...quizFormData.questions, processedQuestion];
    }

    const updatedFormData = {
      ...quizFormData,
      questions: updatedQuestions,
    };

    setQuizFormData(updatedFormData);
    setCurrentQuestion({
      question: "",
      options: [
        { text: "", weight: 0, isCorrect: false },
        { text: "", weight: 0, isCorrect: false },
        { text: "", weight: 0, isCorrect: false },
        { text: "", weight: 0, isCorrect: false }
      ],
      questionType: "single-answer",
      correctAnswer: 0,
    });
    setEditingQuestionIndex(null);
  };

  // Start editing an existing question
  const startEditQuestion = (index) => {
    const questionToEdit = quizFormData.questions[index];

    // Handle both new and legacy question formats
    let processedQuestionForEdit;

    if (questionToEdit.options && questionToEdit.options.length > 0 &&
        typeof questionToEdit.options[0] === 'object' && questionToEdit.options[0].hasOwnProperty('text')) {
      // New format with weighted options
      processedQuestionForEdit = {
        ...questionToEdit,
        correctAnswer: parseInt(questionToEdit.correctAnswer, 10) || 0,
        options: questionToEdit.options.map(opt => ({
          text: opt.text || opt,
          weight: opt.weight || 0,
          isCorrect: opt.isCorrect || false
        }))
      };
    } else {
      // Legacy format - convert to new format
      const correctAnswerIndex = parseInt(questionToEdit.correctAnswer, 10) || 0;
      processedQuestionForEdit = {
        ...questionToEdit,
        correctAnswer: correctAnswerIndex,
        questionType: questionToEdit.questionType || 'single-answer',
        options: (questionToEdit.options || []).map((optionText, i) => ({
          text: optionText,
          weight: i === correctAnswerIndex ? 100 : 0,
          isCorrect: i === correctAnswerIndex
        }))
      };
    }

    setCurrentQuestion(processedQuestionForEdit);
    setEditingQuestionIndex(index);
  };

  // Handle question form changes
  const handleQuestionChange = (e) => {
    setCurrentQuestion({
      ...currentQuestion,
      question: e.target.value,
    });
  };

  // Handle option text changes
  const handleOptionChange = (index, field, value) => {
    const updatedOptions = [...currentQuestion.options];

    if (typeof field === 'string') {
      // New format: field specifies what to update (text, weight, isCorrect)
      updatedOptions[index] = {
        ...updatedOptions[index],
        [field]: value
      };
    } else {
      // Legacy format: field is actually the value for text
      updatedOptions[index] = {
        ...updatedOptions[index],
        text: field
      };
    }

    setCurrentQuestion({
      ...currentQuestion,
      options: updatedOptions,
    });
  };

  // Handle question type change
  const handleQuestionTypeChange = (type) => {
    setCurrentQuestion({
      ...currentQuestion,
      questionType: type,
      // Reset weights when changing question type
      options: currentQuestion.options.map(opt => ({
        ...opt,
        weight: 0,
        isCorrect: false
      }))
    });
  };

  // Set correct answer (legacy single-answer support)
  const setCorrectAnswer = (index) => {
    const updatedOptions = currentQuestion.options.map((opt, i) => ({
      ...opt,
      weight: i === index ? 100 : 0,
      isCorrect: i === index
    }));

    setCurrentQuestion({
      ...currentQuestion,
      correctAnswer: index,
      options: updatedOptions
    });
  };

  // Toggle correct answer for multi-answer questions
  const toggleCorrectAnswer = (index) => {
    const updatedOptions = [...currentQuestion.options];
    updatedOptions[index] = {
      ...updatedOptions[index],
      isCorrect: !updatedOptions[index].isCorrect,
      weight: !updatedOptions[index].isCorrect ? 25 : 0 // Default weight for new correct answers
    };

    setCurrentQuestion({
      ...currentQuestion,
      options: updatedOptions
    });
  };

  // Validate and normalize weights
  const normalizeWeights = () => {
    const correctOptions = currentQuestion.options.filter(opt => opt.isCorrect);
    if (correctOptions.length === 0) return;

    const totalWeight = correctOptions.reduce((sum, opt) => sum + (opt.weight || 0), 0);

    if (totalWeight !== 100) {
      const equalWeight = Math.floor(100 / correctOptions.length);
      const remainder = 100 - (equalWeight * correctOptions.length);

      const updatedOptions = currentQuestion.options.map((opt) => {
        if (opt.isCorrect) {
          const optionIndex = correctOptions.findIndex(co => co === opt);
          return {
            ...opt,
            weight: equalWeight + (optionIndex === 0 ? remainder : 0)
          };
        }
        return { ...opt, weight: 0 };
      });

      setCurrentQuestion({
        ...currentQuestion,
        options: updatedOptions
      });
    }
  };

  // Remove question from quiz
  const removeQuestion = (index) => {
    const updatedQuestions = [...quizFormData.questions];
    updatedQuestions.splice(index, 1);

    setQuizFormData({
      ...quizFormData,
      questions: updatedQuestions,
    });
  };

  // Save quiz
  const saveQuiz = () => {
    const existingQuiz = quizzes.find((quiz) => quiz._id === editingQuizId);
    const moduleOfEditingQuiz = existingQuiz?.module;

    // Check if there's a valid current question that hasn't been added yet
    let updatedQuestions = [...quizFormData.questions];

    // Only add the current question if it has content and all options are filled
    if (
      currentQuestion.question &&
      !currentQuestion.options.some(opt => !opt)
    ) {
      // Create a copy of the current question with correctAnswer as a string
      const processedQuestion = {
        ...currentQuestion,
        correctAnswer: currentQuestion.correctAnswer.toString(), // Convert to string for backend compatibility
      };

      // Add the current question to the questions array
      updatedQuestions.push(processedQuestion);
    }

    const updatedQuiz = {
      ...existingQuiz,
      title: quizFormData.title,
      description: quizFormData.description,
      questions: updatedQuestions,
      passingScore: quizFormData.passingScore,
      // Preserve the isNew flag if it exists
      isNew: existingQuiz?.isNew || false,
    };

    // Update quiz in the quizzes array
    const updatedQuizzes = quizzes.map((quiz) =>
      quiz._id === editingQuizId ? updatedQuiz : quiz
    );
    setQuizzes(updatedQuizzes);

    // Also update the quiz reference in the module
    if (moduleOfEditingQuiz) {
      const moduleToUpdate = modules.find((m) => m._id === moduleOfEditingQuiz);
      if (moduleToUpdate) {
        const updatedModules = modules.map((module) => {
          if (module._id === moduleOfEditingQuiz) {
            // If the module's quiz is an object, update the object
            // If it's an ID, no need to update as the ID hasn't changed
            if (
              typeof module.quiz === "object" &&
              module.quiz?._id === editingQuizId
            ) {
              return { ...module, quiz: updatedQuiz };
            }
            return module;
          }
          return module;
        });
        setModules(updatedModules);
      }
    }

    // Make sure the course data is updated
    setHasUpdatedCourseData(true);

    // Reset form state
    setEditingQuizId(null);
    setQuizFormData({
      title: "",
      description: "",
      questions: [],
      passingScore: 60
    });
    setCurrentQuestion({
      question: "",
      options: ["", "", "", ""],
      correctAnswer: 0,
    });
  };

  // Delete quiz
  const deleteQuiz = async (quizId, moduleId = null) => {
    const confirmed = await showConfirm("Are you sure you want to delete this quiz?");
    if (confirmed) {
      // Remove quiz from array
      setQuizzes(quizzes.filter((quiz) => quiz._id !== quizId));

      // If the quiz is part of a module, update the module
      if (moduleId) {
        const updatedModules = modules.map((module) => {
          // Check if this module has the quiz we're deleting
          if (module._id === moduleId) {
            // Handle both object and string ID references
            if (
              (typeof module.quiz === 'object' && module.quiz?._id === quizId) ||
              module.quiz === quizId
            ) {
              return { ...module, quiz: null };
            }
          }
          return module;
        });

        setModules(updatedModules);
      }

      // Make sure the course data is updated
      setHasUpdatedCourseData(true);
    }
  };

  // Render content item in the list
  const renderContentItem = (contentItem, moduleId = null) => {
    const contentType = contentItem.type || "video";
    let icon;

    switch (contentType) {
      case "video":
        icon = <FaVideo className="content-icon" />;
        break;
      case "document":
        icon = <FaFileAlt className="content-icon" />;
        break;
      case "text":
        icon = <FaFileText className="content-icon" />;
        break;
      case "youtube":
        icon = <FaYoutube className="content-icon" />;
        break;
      default:
        icon = <FaFileAlt className="content-icon" />;
    }

    return (
      <div key={contentItem._id} className="content-item">
        <div className="content-info">
          {icon}
          <span className="content-title">{contentItem.title}</span>
          <span className="content-type-badge">{contentType}</span>
        </div>
        <div className="content-actions">
          <button
            type="button"
            className="edit-grey-btn "
            onClick={() => startEditContent(contentItem)}
          >
            <FaEdit />
          </button>
          <button
            type="button"
            className="delete-grey-btn"
            onClick={() => deleteContent(contentItem._id, moduleId)}
          >
            <FaTrash />
          </button>
        </div>
      </div>
    );
  };

  // Render quiz item
  const renderQuizItem = (quiz, moduleId = null) => {
    // Only add a simple null check to prevent errors
    if (!quiz) return null;

    // Ensure quiz has the required properties
    const quizTitle = quiz.title || "Untitled Quiz";
    const quizDescription = quiz.description || "";
    const questionsCount = quiz.questions?.length || 0;

    return (
      <div className="quiz-item" key={quiz._id}>
        <div className="quiz-item-header">
          <div className="quiz-item-icon">
            <FaQuestionCircle />
          </div>
          <div className="quiz-item-info">
            <h4>{quizTitle}</h4>
            {quizDescription && <p>{quizDescription}</p>}
            <span className="quiz-questions-count">
              {questionsCount} questions
            </span>
          </div>
        </div>
        <div className="quiz-item-actions">
          <button
            onClick={() => {
              setEditingQuizId(quiz._id);
              setQuizFormData({
                title: quizTitle,
                description: quizDescription,
                questions: quiz.questions || [],
                passingScore: quiz.passingScore || 60,
              });
            }}
          >
            <FaEdit />
          </button>
          <button onClick={() => deleteQuiz(quiz._id, moduleId)}>
            <FaTrash />
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="curriculum-step">
      <h2>Course Curriculum</h2>
      <p className="step-description">
        Structure your course content in a way that's easy for students to
        follow. Create modules first, then add content and quizzes to each
        module.
      </p>

      <div className="modules-container">
        <div className="section-header">
          <h3>Modules</h3>
          <button type="button" className="add-button" onClick={addModule}>
            <FaPlus />
            <span>Add Module</span>
          </button>
        </div>

        {/* Module Editor */}
        {editingModuleId && (
          <div className="module-editor">
            <h4>
              {moduleFormData.title
                ? `Edit: ${moduleFormData.title}`
                : "New Module"}
            </h4>
            <div className="form-group">
              <label htmlFor="moduleTitle">Module Title</label>
              <input
                type="text"
                id="moduleTitle"
                value={moduleFormData.title}
                onChange={(e) =>
                  setModuleFormData({
                    ...moduleFormData,
                    title: e.target.value,
                  })
                }
                placeholder="Enter module title"
              />
            </div>
            <div className="form-group">
              <label htmlFor="moduleDescription">Description (Optional)</label>
              <textarea
                id="moduleDescription"
                value={moduleFormData.description}
                onChange={(e) =>
                  setModuleFormData({
                    ...moduleFormData,
                    description: e.target.value,
                  })
                }
                placeholder="Enter module description"
                rows={3}
              />
            </div>
            <div className="form-group checkbox-group">
              <label className="checkbox-container">
                <input
                  type="checkbox"
                  checked={moduleFormData.isCompulsory}
                  onChange={(e) =>
                    setModuleFormData({
                      ...moduleFormData,
                      isCompulsory: e.target.checked,
                    })
                  }
                />
                <span className="checkbox-text">
                  Make this module compulsory
                </span>
              </label>
              <span className="checkbox-hint">
                If checked, learners must complete this module before proceeding
                to the next one. Optional modules can be skipped and don't
                affect certificate eligibility.
              </span>
            </div>
            <div className="form-actions">
              <button
                type="button"
                className="cancel-button"
                onClick={() => setEditingModuleId(null)}
              >
                Cancel
              </button>
              <button
                type="button"
                className="save-button"
                onClick={saveModule}
              >
                Save Module
              </button>
            </div>
          </div>
        )}

        {/* Modules List with Drag and Drop */}
        <DragDropContext onDragEnd={onDragEnd}>
          <Droppable droppableId="modules">
            {(provided) => (
              <div
                {...provided.droppableProps}
                ref={provided.innerRef}
                className="modules-list"
              >
                {modules.length === 0 ? (
                  <div className="no-modules">
                    <p>
                      No modules yet. Click "Add Module" to create your first
                      module.
                    </p>
                  </div>
                ) : (
                  modules.map((module, index) => (
                    <Draggable
                      key={module._id}
                      draggableId={module._id}
                      index={index}
                    >
                      {(provided) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          className="module-card"
                        >
                          <div className="module-header">
                            <div
                              {...provided.dragHandleProps}
                              className="drag-handle"
                            >
                              <FaGripVertical />
                            </div>
                            <h4
                              onClick={() => toggleModuleExpanded(module._id)}
                              className="module-title"
                            >
                              {index + 1}. {module.title}
                              {module.isCompulsory === false ? (
                                <span className="module-optional-badge" style={{ marginLeft: '8px', fontSize: '0.7rem', padding: '2px 6px', backgroundColor: '#f0ad4e', color: 'white', borderRadius: '4px' }}>
                                  Optional
                                </span>
                              ) : (
                                <span className="module-compulsory-badge" style={{ marginLeft: '8px', fontSize: '0.7rem', padding: '2px 6px', backgroundColor: '#5cb85c', color: 'white', borderRadius: '4px' }}>
                                  Compulsory
                                </span>
                              )}
                            </h4>
                            <div className="module-actions">
                              <button
                                className="toggle-module-btn"
                                onClick={() => toggleModuleExpanded(module._id)}
                              >
                                {expandedModules[module._id] ? (
                                  <FaChevronUp />
                                ) : (
                                  <FaChevronDown />
                                )}
                              </button>
                              <button onClick={() => startEditModule(module)}>
                                <FaEdit />
                              </button>
                              <button onClick={() => deleteModule(module._id)}>
                                <FaTrash />
                              </button>
                            </div>
                          </div>

                          {module.description && (
                            <p className="module-description">
                              {module.description}
                            </p>
                          )}

                          {/* Module Content - Shown only when expanded */}
                          {expandedModules[module._id] && (
                            <>
                              <div className="module-content">
                                <h5>Content</h5>
                                <div className="content-list">
                                  {module.content &&
                                    module.content.length > 0 ? (
                                    // First try to find content in the module's content array
                                    module.content
                                      .map((contentId) => {
                                        // If contentId is an object, use it directly
                                        if (typeof contentId === "object") {
                                          return renderContentItem(
                                            contentId,
                                            module._id
                                          );
                                        } else {
                                          // Otherwise, find the content in the content array
                                          const contentItem = content.find(
                                            (item) => item._id === contentId
                                          );
                                          return contentItem
                                            ? renderContentItem(
                                              contentItem,
                                              module._id
                                            )
                                            : null;
                                        }
                                      })
                                      .filter(Boolean) // Remove any null items
                                  ) : (
                                    <p className="no-items">
                                      No content in this module yet.
                                    </p>
                                  )}

                                  {/* Content Editor - Show inside module when editing content for this module */}
                                  {editingContentId &&
                                    content.find(
                                      (c) => c._id === editingContentId
                                    )?.module === module._id && (
                                      <div className="content-editor in-module">
                                        <h4>
                                          {contentFormData.title
                                            ? `Edit: ${contentFormData.title}`
                                            : "New Content"}
                                        </h4>
                                        <div className="form-group">
                                          <label htmlFor="contentTitle">
                                            Content Title
                                          </label>
                                          <input
                                            type="text"
                                            id="contentTitle"
                                            value={contentFormData.title}
                                            onChange={(e) =>
                                              setContentFormData({
                                                ...contentFormData,
                                                title: e.target.value,
                                              })
                                            }
                                            placeholder="Enter content title"
                                          />
                                        </div>
                                        <div className="form-group">
                                          <label htmlFor="contentType">
                                            Content Type
                                          </label>
                                          <select
                                            id="contentType"
                                            value={contentFormData.type}
                                            onChange={(e) =>
                                              setContentFormData({
                                                ...contentFormData,
                                                type: e.target.value,
                                              })
                                            }
                                          >
                                            {/* <option value="video">Video</option> */}
                                            <option value="document">
                                              Document/PDF
                                            </option>
                                            <option value="text">Text</option>
                                            <option value="youtube">
                                              YouTube Link
                                            </option>
                                          </select>
                                        </div>
                                        <div className="form-group">
                                          <label htmlFor="contentDescription">
                                            Description (Optional)
                                          </label>
                                          <textarea
                                            id="contentDescription"
                                            value={contentFormData.description}
                                            onChange={(e) =>
                                              setContentFormData({
                                                ...contentFormData,
                                                description: e.target.value,
                                              })
                                            }
                                            placeholder="Enter content description"
                                            rows={3}
                                          />
                                        </div>
                                        {contentFormData.type === "text" ? (
                                          <div className="form-group">
                                            <label htmlFor="textEditor">
                                              Text Content
                                            </label>
                                            <SummernoteEditor
                                              value={
                                                contentFormData.textContent
                                              }
                                              onChange={(content) =>
                                                setContentFormData({
                                                  ...contentFormData,
                                                  textContent: content,
                                                })
                                              }
                                              placeholder="Enter your text content here..."
                                            />
                                          </div>
                                        ) : contentFormData.type ===
                                          "youtube" ? (
                                          <div className="form-group">
                                            <label htmlFor="youtubeUrl">
                                              YouTube Video URL
                                            </label>
                                            <input
                                              type="text"
                                              id="youtubeUrl"
                                              value={
                                                contentFormData.textContent ||
                                                ""
                                              }
                                              onChange={(e) =>
                                                setContentFormData({
                                                  ...contentFormData,
                                                  textContent: e.target.value,
                                                })
                                              }
                                              placeholder="Enter YouTube video URL (e.g., https://www.youtube.com/watch?v=VIDEO_ID)"
                                            />
                                            {contentFormData.textContent && (
                                              <div className="youtube-preview">
                                                <p>Preview:</p>
                                                <div className="embed-responsive">
                                                  <iframe
                                                    width="100%"
                                                    height="315"
                                                    src={`https://www.youtube.com/embed/${getYoutubeVideoId(
                                                      contentFormData.textContent
                                                    )}`}
                                                    title="YouTube video player"
                                                    style={{ border: 0 }}
                                                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                                    allowFullScreen
                                                  ></iframe>
                                                </div>
                                              </div>
                                            )}
                                          </div>
                                        ) : (
                                          <div className="form-group">
                                            <div className="file-upload">
                                              <input
                                                type="file"
                                                id="contentFile"
                                                onChange={(e) =>
                                                  setContentFormData({
                                                    ...contentFormData,
                                                    file: e.target.files[0],
                                                  })
                                                }
                                                accept={
                                                  contentFormData.type ===
                                                    "video"
                                                    ? "video/*"
                                                    : "application/pdf,application/msword"
                                                }
                                              />
                                              <label
                                                htmlFor="contentFile"
                                                className="file-upload-label"
                                              >
                                                <FaFileUpload />
                                                Upload{" "}
                                                {contentFormData.type ===
                                                  "video"
                                                  ? "Video"
                                                  : "Document"}
                                              </label>
                                              {contentFormData.file ? (
                                                <span className="file-name">
                                                  {contentFormData.file.name}
                                                </span>
                                              ) : contentFormData.existingFileName ? (
                                                <div className="existing-file">
                                                  <span className="file-name">
                                                    Current file: {contentFormData.existingFileName}
                                                  </span>
                                                  <p className="file-hint">
                                                    Upload a new file to replace the current one
                                                  </p>
                                                </div>
                                              ) : null}
                                            </div>
                                          </div>
                                        )}
                                        <div className="form-actions">
                                          <button
                                            type="button"
                                            className="cancel-button"
                                            onClick={() =>
                                              setEditingContentId(null)
                                            }
                                          >
                                            Cancel
                                          </button>
                                          <button
                                            type="button"
                                            className="save-button"
                                            onClick={saveContent}
                                          >
                                            Save Content
                                          </button>
                                        </div>
                                      </div>
                                    )}

                                  <button
                                    className="add-content-button"
                                    onClick={() =>
                                      addContentToModule(module._id)
                                    }
                                  >
                                    <FaPlus />
                                    <span>Add Content</span>
                                  </button>
                                </div>
                              </div>

                              {/* Module Quiz */}
                              <div className="module-quiz">
                                <h5>Quiz</h5>
                                {module.quiz ? (
                                  <div className="quiz-container">
                                    {/* Handle both object and ID references */}
                                    {(() => {
                                      // If quiz is already an object, use it directly
                                      if (typeof module.quiz === "object") {
                                        return renderQuizItem(
                                          module.quiz,
                                          module._id
                                        );
                                      } else {
                                        // Otherwise, find the quiz in the quizzes array
                                        const quiz = quizzes.find(
                                          (q) => q._id === module.quiz
                                        );
                                        return quiz ? (
                                          renderQuizItem(quiz, module._id)
                                        ) : (
                                          <div className="no-quiz">
                                            <p>
                                              Quiz not found. Please add a new
                                              quiz.
                                            </p>
                                            <button
                                              className="add-quiz-button"
                                              onClick={() =>
                                                addQuizToModule(module._id)
                                              }
                                            >
                                              <FaPlus />
                                              <span>Add Quiz</span>
                                            </button>
                                          </div>
                                        );
                                      }
                                    })()}
                                  </div>
                                ) : (
                                  <div className="no-quiz">
                                    <p className="no-items">
                                      No quiz for this module yet.
                                    </p>
                                    <button
                                      className="add-quiz-button"
                                      onClick={() =>
                                        addQuizToModule(module._id)
                                      }
                                    >
                                      <FaPlus />
                                      <span>Add Quiz</span>
                                    </button>
                                  </div>
                                )}

                                {/* Quiz Editor - Show inside module when editing quiz for this module */}
                                {editingQuizId &&
                                  quizzes.find((q) => q._id === editingQuizId)
                                    ?.module === module._id && (
                                    <div className="quiz-editor in-module">
                                      <h4>
                                        {quizFormData.title
                                          ? `Edit: ${quizFormData.title}`
                                          : "New Quiz"}
                                      </h4>
                                      <div className="form-group">
                                        <label htmlFor="quizTitle">
                                          Quiz Title
                                        </label>
                                        <input
                                          type="text"
                                          id="quizTitle"
                                          value={quizFormData.title}
                                          onChange={(e) =>
                                            setQuizFormData({
                                              ...quizFormData,
                                              title: e.target.value,
                                            })
                                          }
                                          placeholder="Enter quiz title"
                                        />
                                      </div>
                                      <div className="form-group">
                                        <label htmlFor="quizDescription">
                                          Description (Optional)
                                        </label>
                                        <textarea
                                          id="quizDescription"
                                          value={quizFormData.description}
                                          onChange={(e) =>
                                            setQuizFormData({
                                              ...quizFormData,
                                              description: e.target.value,
                                            })
                                          }
                                          placeholder="Enter quiz description"
                                          rows={3}
                                        />
                                      </div>

                                      {/* Quiz Configuration */}
                                      <div className="quiz-config-section">
                                        <h5>Quiz Configuration</h5>
                                        <div className="quiz-config-row">
                                          <div className="form-group">
                                            <label htmlFor="passingScore">
                                              Passing Score (%)
                                            </label>
                                            <input
                                              type="number"
                                              id="passingScore"
                                              min="1"
                                              max="100"
                                              value={quizFormData.passingScore}
                                              onChange={(e) =>
                                                setQuizFormData({
                                                  ...quizFormData,
                                                  passingScore: parseInt(e.target.value) || 60,
                                                })
                                              }
                                              placeholder="60"
                                            />
                                            <small className="field-hint">
                                              Students must achieve this score to pass the quiz and be eligible for certificates
                                            </small>
                                          </div>
                                        </div>
                                      </div>

                                      {/* Questions List */}
                                      <div className="questions-list">
                                        <h5>
                                          Questions (
                                          {quizFormData.questions.length})
                                        </h5>
                                        {quizFormData.questions.map(
                                          (q, index) => (
                                            <div
                                              key={index}
                                              className="question-item"
                                            >
                                              <div className="question-header">
                                                <h6>
                                                  Question {index + 1}:{" "}
                                                  {q.question}
                                                </h6>
                                                <div className="question-actions">
                                                  <button
                                                    type="button"
                                                    className="new-edit-question"
                                                    onClick={() =>
                                                      startEditQuestion(index)
                                                    }
                                                    title="Edit Question"
                                                  >
                                                    <FaEdit />
                                                  </button>
                                                  <button
                                                    type="button"
                                                    className="new-remove-question"
                                                    onClick={() =>
                                                      removeQuestion(index)
                                                    }
                                                    title="Delete Question"
                                                  >
                                                    <FaTrash />
                                                  </button>
                                                </div>
                                              </div>
                                              <ul className="options-list">
                                                {q.options.map(
                                                  (option, optIndex) => {
                                                    // Handle both new and legacy option formats
                                                    const optionText = typeof option === 'object' ? option.text : option;
                                                    const optionWeight = typeof option === 'object' ? option.weight : 0;
                                                    const isCorrect = typeof option === 'object' ? option.isCorrect : false;

                                                    // Legacy fallback
                                                    const correctAnswerNum = typeof q.correctAnswer === "string"
                                                      ? parseInt(q.correctAnswer, 10)
                                                      : q.correctAnswer;
                                                    const isLegacyCorrect = optIndex === correctAnswerNum;

                                                    return (
                                                      <li
                                                        key={optIndex}
                                                        className={
                                                          (isCorrect || isLegacyCorrect) ? "correct" : ""
                                                        }
                                                      >
                                                        {optionText}
                                                        {(isCorrect || isLegacyCorrect) && (
                                                          <span className="option-weight">
                                                            {optionWeight > 0 ? ` (${optionWeight}%)` : ' (Correct)'}
                                                          </span>
                                                        )}
                                                      </li>
                                                    );
                                                  }
                                                )}
                                              </ul>
                                            </div>
                                          )
                                        )}
                                      </div>

                                      {/* Add Question Form */}
                                      <div className="add-question-form">
                                        <h5>
                                          {editingQuestionIndex !== null
                                            ? "Edit Question"
                                            : "Add New Question"}
                                        </h5>
                                        <div className="form-group">
                                          <label htmlFor="questionText">
                                            Question
                                          </label>
                                          <input
                                            type="text"
                                            id="questionText"
                                            value={currentQuestion.question}
                                            onChange={handleQuestionChange}
                                            placeholder="Enter question"
                                          />
                                        </div>
                                        <div className="form-group">
                                          <label htmlFor="questionType">
                                            Question Type
                                          </label>
                                          <select
                                            id="questionType"
                                            value={currentQuestion.questionType || 'single-answer'}
                                            onChange={(e) => handleQuestionTypeChange(e.target.value)}
                                          >
                                            <option value="single-answer">Single Answer</option>
                                            <option value="multi-answer">Multiple Answers (Partial Credit)</option>
                                          </select>
                                        </div>
                                        <div className="options-container">
                                          <label>
                                            Options {currentQuestion.questionType === 'multi-answer' ? '(set weights for partial credit)' : '(select one as correct)'}
                                          </label>
                                          {currentQuestion.options.map(
                                            (option, index) => (
                                              <div
                                                key={index}
                                                className="option-row enhanced"
                                              >
                                                <input
                                                  type="text"
                                                  value={option.text || ''}
                                                  onChange={(e) =>
                                                    handleOptionChange(
                                                      index,
                                                      'text',
                                                      e.target.value
                                                    )
                                                  }
                                                  placeholder={`Option ${index + 1}`}
                                                  className="option-text-input"
                                                />

                                                {currentQuestion.questionType === 'multi-answer' ? (
                                                  <div className="multi-answer-controls">
                                                    <button
                                                      type="button"
                                                      className={`correct-toggle ${option.isCorrect ? "active" : ""}`}
                                                      onClick={() => toggleCorrectAnswer(index)}
                                                    >
                                                      {option.isCorrect ? "✓" : "○"}
                                                    </button>
                                                    {option.isCorrect && (
                                                      <input
                                                        type="number"
                                                        min="0"
                                                        max="100"
                                                        value={option.weight || 0}
                                                        onChange={(e) =>
                                                          handleOptionChange(
                                                            index,
                                                            'weight',
                                                            parseInt(e.target.value) || 0
                                                          )
                                                        }
                                                        placeholder="Weight %"
                                                        className="weight-input"
                                                      />
                                                    )}
                                                  </div>
                                                ) : (
                                                  <button
                                                    type="button"
                                                    className={`correct-toggle ${index === currentQuestion.correctAnswer ? "active" : ""}`}
                                                    onClick={() => setCorrectAnswer(index)}
                                                  >
                                                    {index === currentQuestion.correctAnswer ? "Correct" : "Mark as Correct"}
                                                  </button>
                                                )}
                                              </div>
                                            )
                                          )}

                                          {currentQuestion.questionType === 'multi-answer' && (
                                            <div className="weight-summary">
                                              <p>Total Weight: {currentQuestion.options.reduce((sum, opt) => sum + (opt.weight || 0), 0)}% (must equal 100%)</p>
                                              <button
                                                type="button"
                                                className="normalize-weights-btn"
                                                onClick={normalizeWeights}
                                              >
                                                Auto-Balance Weights
                                              </button>
                                            </div>
                                          )}
                                        </div>
                                        <div className="question-form-actions">
                                          {editingQuestionIndex !== null && (
                                            <button
                                              type="button"
                                              className="cancel-edit-button"
                                              onClick={() => {
                                                setEditingQuestionIndex(null);
                                                setCurrentQuestion({
                                                  question: "",
                                                  options: [
                                                    { text: "", weight: 0, isCorrect: false },
                                                    { text: "", weight: 0, isCorrect: false },
                                                    { text: "", weight: 0, isCorrect: false },
                                                    { text: "", weight: 0, isCorrect: false }
                                                  ],
                                                  questionType: "single-answer",
                                                  correctAnswer: 0,
                                                });
                                              }}
                                            >
                                              Cancel Edit
                                            </button>
                                          )}
                                          <button
                                            type="button"
                                            className="add-question-button"
                                            onClick={addQuestionToQuiz}
                                          >
                                            {editingQuestionIndex !== null ? (
                                              <>
                                                <FaEdit />
                                                <span>Update Question</span>
                                              </>
                                            ) : (
                                              <>
                                                <FaPlus />
                                                <span>Add Question</span>
                                              </>
                                            )}
                                          </button>
                                        </div>
                                      </div>

                                      <div className="form-actions">
                                        <button
                                          type="button"
                                          className="cancel-button"
                                          onClick={() => setEditingQuizId(null)}
                                        >
                                          Cancel
                                        </button>
                                        <button
                                          type="button"
                                          className="save-button"
                                          onClick={saveQuiz}
                                        >
                                          Save Quiz
                                        </button>
                                      </div>
                                    </div>
                                  )}
                              </div>
                            </>
                          )}
                        </div>
                      )}
                    </Draggable>
                  ))
                )}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      </div>
    </div>
  );
};

// Helper function to extract YouTube video ID from URL
const getYoutubeVideoId = (url) => {
  if (!url) return "";

  // Extract video ID from different YouTube URL formats
  const regExp =
    /^.*((youtu.be\/)|(v\/)|(\/u\/\w\/)|(embed\/)|(watch\?))\??v?=?([^#&?]*).*/;
  const match = url.match(regExp);

  return match && match[7].length === 11 ? match[7] : "";
};

export default CurriculumStep;
