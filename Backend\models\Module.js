const mongoose = require("mongoose");

const moduleSchema = new mongoose.Schema(
  {
    title: { type: String, required: true },
    description: { type: String },
    course: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Course",
      required: true,
    },
    content: [{ type: mongoose.Schema.Types.ObjectId, ref: "Content" }],
    quiz: { type: mongoose.Schema.Types.ObjectId, ref: "Quiz" }, // Reference to associated quiz
    order: { type: Number, default: 0 }, // For ordering modules within a course
    isCompulsory: { type: Boolean, default: true }, // Flag to indicate if module is compulsory or optional

    // Quiz completion requirements for this module
    quizRequirements: {
      requireQuizCompletion: { type: <PERSON><PERSON>an, default: false }, // Whether quiz must be passed to complete module
      customPassingScore: { type: Number }, // Override quiz's default passing score for this module
      allowProgressWithoutQuiz: { type: Boolean, default: true }, // Allow content completion without quiz for optional modules
    },

    // Module completion criteria
    completionCriteria: {
      requireAllContent: { type: Boolean, default: true }, // Must complete all content
      requireQuizPass: { type: Boolean, default: false }, // Must pass quiz (if exists)
      minimumQuizScore: { type: Number }, // Minimum quiz score required (overrides quiz's passing score)
    }
  },
  { timestamps: true }
);

module.exports = mongoose.model("Module", moduleSchema);
