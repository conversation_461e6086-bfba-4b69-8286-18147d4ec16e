.newsletter-page {
  
width:100%;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  color: #333;
  margin-bottom: 5px;
}

.page-header p {
  color: #666;
  margin: 0;
}

.tab-navigation {
  display: flex;
  border-bottom: 2px solid #e0e0e0;
  margin-bottom: 30px;
}

.tab-navigation button {
  padding: 12px 24px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 16px;
  color: #666;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
}

.tab-navigation button:hover {
  color: #007bff;
}

.tab-navigation button.active {
  color: #007bff;
  border-bottom-color: #007bff;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon {
  font-size: 24px;
  color: #007bff;
}

.stat-content h3 {
  margin: 0;
  font-size: 24px;
  color: #333;
}

.stat-content p {
  margin: 5px 0 0 0;
  color: #666;
  font-size: 14px;
}

.recent-activity {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.recent-activity h3 {
  margin: 0 0 10px 0;
  color: #333;
}

.newsletter-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.newsletter-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background-color: var(--white);
}

.newsletter-info h4 {
  margin: 0 0 5px 0;
  color: #333;
}

.newsletter-info p {
  margin: 0 0 5px 0;
  color: #666;
  font-size: 14px;
}

.newsletter-info small {
  color: #999;
  font-size: 12px;
}

.newsletter-actions {
  display: flex;
  gap: 10px;
}

.btn-primary, .btn-secondary, .btn-edit, .btn-delete, .btn-send {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: all 0.3s ease;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: var(--primary-hover-color);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
}

.btn-edit {
  background: #28a745;
  color: white;
  padding: 6px 10px;
}

.btn-edit:hover {
  background: #1e7e34;
}

.btn-delete {
  background: #dc3545;
  color: white;
  padding: 6px 10px;
}

.btn-delete:hover {
  background: #c82333;
}

.btn-send {
  background: #17a2b8;
  color: white;
  padding: 6px 10px;
}

.btn-send:hover {
  background: #138496;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
  color: #333;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal {
  background: white;
  padding: 30px;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
}

.modal h4 {
  margin-top: 0;
  color: #333;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  color: #333;
  font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.subscriber-list table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.subscriber-list th,
.subscriber-list td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

.subscriber-list th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status.active {
  background: #d4edda;
  color: #155724;
}

.status.inactive {
  background: #f8d7da;
  color: #721c24;
}

.preferences {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}

.pref-tag {
  background: #e9ecef;
  color: #495057;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #666;
}

/* Newsletter Pagination Styles */
.newsletter-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  margin-top: 20px;
  padding: 20px 0;
}

.btn-see-more,
.btn-see-less {
  background: var(--primary-color);
  color: var(--white);
  border: none;
  padding: 12px 24px;
  border-radius: var(--border-medium-small-radius);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--box-shadow-light);
}

.btn-see-more:hover,
.btn-see-less:hover {
  background: var(--primary-hover-color);
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

.btn-see-less {
  background: var(--text-gray);
}

.btn-see-less:hover {
  background: var(--text-color);
}

@media (max-width: 768px) {

  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .newsletter-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .newsletter-actions {
    align-self: flex-end;
  }
  
  .subscriber-list {
    overflow-x: auto;
  }
  
  .modal {
    width: 95%;
    margin: 20px;
  }

  .newsletter-pagination {
    flex-direction: column;
    gap: 10px;
  }

  .btn-see-more,
  .btn-see-less {
    width: 100%;
    max-width: 250px;
    padding: 14px 20px;
    font-size: var(--smallfont);
  }
}
